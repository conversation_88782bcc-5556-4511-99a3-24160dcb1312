#!/bin/bash

# Distributed Kernel Test Script
# Comprehensive testing of all system components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🧪 Distributed Kernel Test Suite${NC}\n"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is responding
check_port() {
    local port=$1
    local service=$2
    if curl -s -f "http://localhost:$port" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ $service responding on port $port${NC}"
        return 0
    else
        echo -e "${RED}✗ $service not responding on port $port${NC}"
        return 1
    fi
}

# Function to test API endpoint
test_api() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    local status=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint")
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✓ $description (HTTP $status)${NC}"
        return 0
    else
        echo -e "${RED}✗ $description (HTTP $status, expected $expected_status)${NC}"
        return 1
    fi
}

# Test 1: Prerequisites Check
echo -e "${BLUE}Test 1: Prerequisites Check${NC}"
test_count=0
pass_count=0

if command_exists "cargo"; then
    echo -e "${GREEN}✓ Rust/Cargo installed${NC}"
    ((pass_count++))
else
    echo -e "${RED}✗ Rust/Cargo not found${NC}"
fi
((test_count++))

if command_exists "go"; then
    echo -e "${GREEN}✓ Go installed${NC}"
    ((pass_count++))
else
    echo -e "${RED}✗ Go not found${NC}"
fi
((test_count++))

if command_exists "node"; then
    echo -e "${GREEN}✓ Node.js installed${NC}"
    ((pass_count++))
else
    echo -e "${RED}✗ Node.js not found${NC}"
fi
((test_count++))

if command_exists "npm"; then
    echo -e "${GREEN}✓ npm installed${NC}"
    ((pass_count++))
else
    echo -e "${RED}✗ npm not found${NC}"
fi
((test_count++))

echo -e "\n${YELLOW}Prerequisites: $pass_count/$test_count passed${NC}\n"

# Test 2: Build Tests
echo -e "${BLUE}Test 2: Build Tests${NC}"
build_test_count=0
build_pass_count=0

# Test Rust kernel build
echo -e "${YELLOW}Testing Rust kernel build...${NC}"
cd kernel
if cargo check --quiet; then
    echo -e "${GREEN}✓ Kernel builds successfully${NC}"
    ((build_pass_count++))
else
    echo -e "${RED}✗ Kernel build failed${NC}"
fi
((build_test_count++))
cd ..

# Test Go services
echo -e "${YELLOW}Testing Go services...${NC}"
cd services
if go mod verify && go build ./...; then
    echo -e "${GREEN}✓ Go services build successfully${NC}"
    ((build_pass_count++))
else
    echo -e "${RED}✗ Go services build failed${NC}"
fi
((build_test_count++))
cd ..

# Test TypeScript bridge
echo -e "${YELLOW}Testing TypeScript bridge...${NC}"
cd bridge
if npm install --silent && npm run build --silent; then
    echo -e "${GREEN}✓ Bridge service builds successfully${NC}"
    ((build_pass_count++))
else
    echo -e "${RED}✗ Bridge service build failed${NC}"
fi
((build_test_count++))
cd ..

# Test React web interface
echo -e "${YELLOW}Testing React web interface...${NC}"
cd web
if npm install --silent; then
    echo -e "${GREEN}✓ Web interface dependencies installed${NC}"
    ((build_pass_count++))
else
    echo -e "${RED}✗ Web interface dependency installation failed${NC}"
fi
((build_test_count++))
cd ..

echo -e "\n${YELLOW}Build Tests: $build_pass_count/$build_test_count passed${NC}\n"

# Test 3: Unit Tests
echo -e "${BLUE}Test 3: Unit Tests${NC}"
unit_test_count=0
unit_pass_count=0

# Rust unit tests
echo -e "${YELLOW}Running Rust unit tests...${NC}"
cd kernel
if cargo test --quiet; then
    echo -e "${GREEN}✓ Rust unit tests passed${NC}"
    ((unit_pass_count++))
else
    echo -e "${RED}✗ Rust unit tests failed${NC}"
fi
((unit_test_count++))
cd ..

# Go unit tests
echo -e "${YELLOW}Running Go unit tests...${NC}"
cd services
if go test ./... -v; then
    echo -e "${GREEN}✓ Go unit tests passed${NC}"
    ((unit_pass_count++))
else
    echo -e "${RED}✗ Go unit tests failed${NC}"
fi
((unit_test_count++))
cd ..

echo -e "\n${YELLOW}Unit Tests: $unit_pass_count/$unit_test_count passed${NC}\n"

# Test 4: Integration Tests (if system is running)
echo -e "${BLUE}Test 4: Integration Tests${NC}"
integration_test_count=0
integration_pass_count=0

# Check if services are running
echo -e "${YELLOW}Checking if system is running...${NC}"

# Test bridge service health
if test_api "http://localhost:8080/health" "200" "Bridge service health check"; then
    ((integration_pass_count++))
fi
((integration_test_count++))

# Test bridge API endpoints
if test_api "http://localhost:8080/api/stats" "200" "System stats API"; then
    ((integration_pass_count++))
fi
((integration_test_count++))

if test_api "http://localhost:8080/api/processes" "200" "Processes API"; then
    ((integration_pass_count++))
fi
((integration_test_count++))

if test_api "http://localhost:8080/api/files" "200" "Files API"; then
    ((integration_pass_count++))
fi
((integration_test_count++))

if test_api "http://localhost:8080/api/network" "200" "Network API"; then
    ((integration_pass_count++))
fi
((integration_test_count++))

# Test web interface
if check_port 3000 "Web interface"; then
    ((integration_pass_count++))
fi
((integration_test_count++))

echo -e "\n${YELLOW}Integration Tests: $integration_pass_count/$integration_test_count passed${NC}\n"

# Test 5: Performance Tests
echo -e "${BLUE}Test 5: Performance Tests${NC}"
perf_test_count=0
perf_pass_count=0

if command_exists "curl"; then
    echo -e "${YELLOW}Testing API response times...${NC}"
    
    # Test health endpoint response time
    response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:8080/health" 2>/dev/null || echo "999")
    if (( $(echo "$response_time < 1.0" | bc -l 2>/dev/null || echo "0") )); then
        echo -e "${GREEN}✓ Health endpoint responds in ${response_time}s${NC}"
        ((perf_pass_count++))
    else
        echo -e "${RED}✗ Health endpoint slow response: ${response_time}s${NC}"
    fi
    ((perf_test_count++))
    
    # Test stats endpoint response time
    response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:8080/api/stats" 2>/dev/null || echo "999")
    if (( $(echo "$response_time < 2.0" | bc -l 2>/dev/null || echo "0") )); then
        echo -e "${GREEN}✓ Stats endpoint responds in ${response_time}s${NC}"
        ((perf_pass_count++))
    else
        echo -e "${RED}✗ Stats endpoint slow response: ${response_time}s${NC}"
    fi
    ((perf_test_count++))
else
    echo -e "${YELLOW}Skipping performance tests (curl not available)${NC}"
fi

echo -e "\n${YELLOW}Performance Tests: $perf_pass_count/$perf_test_count passed${NC}\n"

# Test Summary
total_tests=$((test_count + build_test_count + unit_test_count + integration_test_count + perf_test_count))
total_passed=$((pass_count + build_pass_count + unit_pass_count + integration_pass_count + perf_pass_count))

echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                                                                              ║${NC}"
echo -e "${PURPLE}║                           TEST SUMMARY                                       ║${NC}"
echo -e "${PURPLE}║                                                                              ║${NC}"
echo -e "${PURPLE}║  Prerequisites:    $pass_count/$test_count passed                                           ║${NC}"
echo -e "${PURPLE}║  Build Tests:      $build_pass_count/$build_test_count passed                                           ║${NC}"
echo -e "${PURPLE}║  Unit Tests:       $unit_pass_count/$unit_test_count passed                                             ║${NC}"
echo -e "${PURPLE}║  Integration:      $integration_pass_count/$integration_test_count passed                                           ║${NC}"
echo -e "${PURPLE}║  Performance:      $perf_pass_count/$perf_test_count passed                                             ║${NC}"
echo -e "${PURPLE}║                                                                              ║${NC}"
echo -e "${PURPLE}║  TOTAL:            $total_passed/$total_tests passed                                           ║${NC}"
echo -e "${PURPLE}║                                                                              ║${NC}"

if [ $total_passed -eq $total_tests ]; then
    echo -e "${PURPLE}║  STATUS:           ${GREEN}ALL TESTS PASSED! 🎉${PURPLE}                               ║${NC}"
    exit_code=0
elif [ $total_passed -gt $((total_tests / 2)) ]; then
    echo -e "${PURPLE}║  STATUS:           ${YELLOW}MOSTLY PASSING ⚠️${PURPLE}                                ║${NC}"
    exit_code=1
else
    echo -e "${PURPLE}║  STATUS:           ${RED}TESTS FAILING ❌${PURPLE}                                 ║${NC}"
    exit_code=2
fi

echo -e "${PURPLE}║                                                                              ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"

# Recommendations
if [ $total_passed -ne $total_tests ]; then
    echo -e "\n${YELLOW}Recommendations:${NC}"
    
    if [ $pass_count -ne $test_count ]; then
        echo -e "${YELLOW}• Install missing prerequisites${NC}"
    fi
    
    if [ $build_pass_count -ne $build_test_count ]; then
        echo -e "${YELLOW}• Fix build issues before running the system${NC}"
    fi
    
    if [ $integration_pass_count -ne $integration_test_count ]; then
        echo -e "${YELLOW}• Start the system with ./start.sh and re-run tests${NC}"
    fi
    
    echo -e "${YELLOW}• Check logs in the logs/ directory for detailed error information${NC}"
fi

exit $exit_code
