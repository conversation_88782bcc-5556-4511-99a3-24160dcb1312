package main

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"distributed-kernel/services/common"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

// NetworkService implements the distributed network stack
type NetworkService struct {
	logger      *common.Logger
	id          uuid.UUID
	startTime   time.Time
	interfaces  map[string]*NetworkInterface
	connections map[uuid.UUID]*Connection
	routes      map[string]*Route
	mutex       sync.RWMutex
}

// NetworkInterface represents a virtual network interface
type NetworkInterface struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Type        string    `json:"type"`
	IPAddress   string    `json:"ip_address"`
	Netmask     string    `json:"netmask"`
	Gateway     string    `json:"gateway"`
	MTU         int       `json:"mtu"`
	Status      string    `json:"status"`
	BytesRx     uint64    `json:"bytes_rx"`
	BytesTx     uint64    `json:"bytes_tx"`
	PacketsRx   uint64    `json:"packets_rx"`
	PacketsTx   uint64    `json:"packets_tx"`
	ErrorsRx    uint64    `json:"errors_rx"`
	ErrorsTx    uint64    `json:"errors_tx"`
	CreatedAt   time.Time `json:"created_at"`
}

// Connection represents a network connection
type Connection struct {
	ID           uuid.UUID `json:"id"`
	Type         string    `json:"type"`
	LocalAddr    string    `json:"local_addr"`
	RemoteAddr   string    `json:"remote_addr"`
	State        string    `json:"state"`
	ProcessID    uuid.UUID `json:"process_id"`
	BytesSent    uint64    `json:"bytes_sent"`
	BytesRecv    uint64    `json:"bytes_recv"`
	PacketsSent  uint64    `json:"packets_sent"`
	PacketsRecv  uint64    `json:"packets_recv"`
	CreatedAt    time.Time `json:"created_at"`
	LastActivity time.Time `json:"last_activity"`
}

// Route represents a network route
type Route struct {
	ID          uuid.UUID `json:"id"`
	Destination string    `json:"destination"`
	Gateway     string    `json:"gateway"`
	Interface   string    `json:"interface"`
	Metric      int       `json:"metric"`
	CreatedAt   time.Time `json:"created_at"`
}

// Packet represents a network packet
type Packet struct {
	ID        uuid.UUID `json:"id"`
	Source    string    `json:"source"`
	Dest      string    `json:"dest"`
	Protocol  string    `json:"protocol"`
	Size      int       `json:"size"`
	Data      []byte    `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// NewNetworkService creates a new network service
func NewNetworkService() *NetworkService {
	logger := common.NewLogger("network", "info")
	
	ns := &NetworkService{
		logger:      logger,
		id:          uuid.New(),
		startTime:   time.Now(),
		interfaces:  make(map[string]*NetworkInterface),
		connections: make(map[uuid.UUID]*Connection),
		routes:      make(map[string]*Route),
	}
	
	// Initialize default network interfaces
	ns.initializeInterfaces()
	
	return ns
}

// initializeInterfaces sets up default network interfaces
func (ns *NetworkService) initializeInterfaces() {
	now := time.Now()
	
	// Loopback interface
	loopback := &NetworkInterface{
		ID:        uuid.New(),
		Name:      "lo",
		Type:      "loopback",
		IPAddress: "127.0.0.1",
		Netmask:   "*********",
		Gateway:   "",
		MTU:       65536,
		Status:    "up",
		CreatedAt: now,
	}
	ns.interfaces["lo"] = loopback
	
	// Ethernet interface
	eth0 := &NetworkInterface{
		ID:        uuid.New(),
		Name:      "eth0",
		Type:      "ethernet",
		IPAddress: "*************",
		Netmask:   "*************",
		Gateway:   "***********",
		MTU:       1500,
		Status:    "up",
		CreatedAt: now,
	}
	ns.interfaces["eth0"] = eth0
	
	// Add default routes
	ns.addRoute("0.0.0.0/0", "***********", "eth0", 100)
	ns.addRoute("*********/8", "", "lo", 0)
	ns.addRoute("***********/24", "", "eth0", 0)
	
	ns.logger.Info("Initialized network interfaces")
}

// CreateSocket creates a new network socket
func (ns *NetworkService) CreateSocket(processID uuid.UUID, socketType string) (uuid.UUID, error) {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()
	
	conn := &Connection{
		ID:           uuid.New(),
		Type:         socketType,
		State:        "created",
		ProcessID:    processID,
		CreatedAt:    time.Now(),
		LastActivity: time.Now(),
	}
	
	ns.connections[conn.ID] = conn
	
	ns.logger.InfoWithFields("Created socket", logrus.Fields{
		"socket_id":  conn.ID,
		"type":       socketType,
		"process_id": processID,
	})
	
	return conn.ID, nil
}

// Bind binds a socket to an address
func (ns *NetworkService) Bind(socketID uuid.UUID, address string) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()
	
	conn, exists := ns.connections[socketID]
	if !exists {
		return fmt.Errorf("socket not found: %s", socketID)
	}
	
	conn.LocalAddr = address
	conn.State = "bound"
	conn.LastActivity = time.Now()
	
	ns.logger.InfoWithFields("Bound socket", logrus.Fields{
		"socket_id": socketID,
		"address":   address,
	})
	
	return nil
}

// Listen puts a socket in listening state
func (ns *NetworkService) Listen(socketID uuid.UUID, backlog int) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()
	
	conn, exists := ns.connections[socketID]
	if !exists {
		return fmt.Errorf("socket not found: %s", socketID)
	}
	
	if conn.State != "bound" {
		return fmt.Errorf("socket must be bound before listening")
	}
	
	conn.State = "listening"
	conn.LastActivity = time.Now()
	
	ns.logger.InfoWithFields("Socket listening", logrus.Fields{
		"socket_id": socketID,
		"backlog":   backlog,
	})
	
	return nil
}

// Connect connects a socket to a remote address
func (ns *NetworkService) Connect(socketID uuid.UUID, remoteAddr string) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()
	
	conn, exists := ns.connections[socketID]
	if !exists {
		return fmt.Errorf("socket not found: %s", socketID)
	}
	
	conn.RemoteAddr = remoteAddr
	conn.State = "connected"
	conn.LastActivity = time.Now()
	
	ns.logger.InfoWithFields("Socket connected", logrus.Fields{
		"socket_id":   socketID,
		"remote_addr": remoteAddr,
	})
	
	return nil
}

// Send sends data through a socket
func (ns *NetworkService) Send(socketID uuid.UUID, data []byte) (int, error) {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()
	
	conn, exists := ns.connections[socketID]
	if !exists {
		return 0, fmt.Errorf("socket not found: %s", socketID)
	}
	
	if conn.State != "connected" {
		return 0, fmt.Errorf("socket not connected")
	}
	
	// Simulate sending data
	bytesSent := len(data)
	conn.BytesSent += uint64(bytesSent)
	conn.PacketsSent++
	conn.LastActivity = time.Now()
	
	// Update interface statistics
	if iface, exists := ns.interfaces["eth0"]; exists {
		iface.BytesTx += uint64(bytesSent)
		iface.PacketsTx++
	}
	
	ns.logger.DebugWithFields("Sent data", logrus.Fields{
		"socket_id": socketID,
		"bytes":     bytesSent,
	})
	
	return bytesSent, nil
}

// Receive receives data from a socket
func (ns *NetworkService) Receive(socketID uuid.UUID, bufferSize int) ([]byte, error) {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()
	
	conn, exists := ns.connections[socketID]
	if !exists {
		return nil, fmt.Errorf("socket not found: %s", socketID)
	}
	
	if conn.State != "connected" {
		return nil, fmt.Errorf("socket not connected")
	}
	
	// Simulate receiving data
	data := []byte("Hello from network service!")
	if len(data) > bufferSize {
		data = data[:bufferSize]
	}
	
	conn.BytesRecv += uint64(len(data))
	conn.PacketsRecv++
	conn.LastActivity = time.Now()
	
	// Update interface statistics
	if iface, exists := ns.interfaces["eth0"]; exists {
		iface.BytesRx += uint64(len(data))
		iface.PacketsRx++
	}
	
	ns.logger.DebugWithFields("Received data", logrus.Fields{
		"socket_id": socketID,
		"bytes":     len(data),
	})
	
	return data, nil
}

// CloseSocket closes a network socket
func (ns *NetworkService) CloseSocket(socketID uuid.UUID) error {
	ns.mutex.Lock()
	defer ns.mutex.Unlock()
	
	conn, exists := ns.connections[socketID]
	if !exists {
		return fmt.Errorf("socket not found: %s", socketID)
	}
	
	conn.State = "closed"
	delete(ns.connections, socketID)
	
	ns.logger.InfoWithFields("Closed socket", logrus.Fields{
		"socket_id": socketID,
	})
	
	return nil
}

// addRoute adds a network route
func (ns *NetworkService) addRoute(destination, gateway, iface string, metric int) {
	route := &Route{
		ID:          uuid.New(),
		Destination: destination,
		Gateway:     gateway,
		Interface:   iface,
		Metric:      metric,
		CreatedAt:   time.Now(),
	}
	
	ns.routes[destination] = route
}

// GetInterfaces returns all network interfaces
func (ns *NetworkService) GetInterfaces() []common.NetworkInfo {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()
	
	var interfaces []common.NetworkInfo
	
	for _, iface := range ns.interfaces {
		interfaces = append(interfaces, common.NetworkInfo{
			Interface: iface.Name,
			IPAddress: iface.IPAddress,
			Netmask:   iface.Netmask,
			Gateway:   iface.Gateway,
			MTU:       iface.MTU,
			BytesRx:   iface.BytesRx,
			BytesTx:   iface.BytesTx,
			PacketsRx: iface.PacketsRx,
			PacketsTx: iface.PacketsTx,
			ErrorsRx:  iface.ErrorsRx,
			ErrorsTx:  iface.ErrorsTx,
		})
	}
	
	return interfaces
}

// GetConnections returns all active connections
func (ns *NetworkService) GetConnections() []*Connection {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()
	
	var connections []*Connection
	for _, conn := range ns.connections {
		connections = append(connections, conn)
	}
	
	return connections
}

// GetStats returns network statistics
func (ns *NetworkService) GetStats() map[string]interface{} {
	ns.mutex.RLock()
	defer ns.mutex.RUnlock()
	
	totalConnections := len(ns.connections)
	totalInterfaces := len(ns.interfaces)
	totalRoutes := len(ns.routes)
	
	var totalBytesRx, totalBytesTx uint64
	for _, iface := range ns.interfaces {
		totalBytesRx += iface.BytesRx
		totalBytesTx += iface.BytesTx
	}
	
	return map[string]interface{}{
		"total_connections": totalConnections,
		"total_interfaces":  totalInterfaces,
		"total_routes":      totalRoutes,
		"total_bytes_rx":    totalBytesRx,
		"total_bytes_tx":    totalBytesTx,
		"uptime":            time.Since(ns.startTime).Seconds(),
	}
}

func main() {
	ns := NewNetworkService()
	
	// Create gRPC server
	lis, err := net.Listen("tcp", ":50052")
	if err != nil {
		ns.logger.FatalWithFields("Failed to listen", logrus.Fields{"error": err})
	}
	
	s := grpc.NewServer()
	
	ns.logger.Info("Network service starting on :50052")
	
	// Handle graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	
	go func() {
		<-c
		ns.logger.Info("Shutting down network service...")
		s.GracefulStop()
	}()
	
	if err := s.Serve(lis); err != nil {
		ns.logger.FatalWithFields("Failed to serve", logrus.Fields{"error": err})
	}
}
