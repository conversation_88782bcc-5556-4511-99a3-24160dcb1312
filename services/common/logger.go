package common

import (
	"os"
	"time"

	"github.com/sirupsen/logrus"
)

// <PERSON><PERSON> wraps logrus with additional functionality
type Logger struct {
	*logrus.Logger
	serviceName string
}

// NewLogger creates a new logger instance
func NewLogger(serviceName string, level string) *Logger {
	logger := logrus.New()
	
	// Set log level
	switch level {
	case "debug":
		logger.SetLevel(logrus.DebugLevel)
	case "info":
		logger.SetLevel(logrus.InfoLevel)
	case "warn":
		logger.SetLevel(logrus.WarnLevel)
	case "error":
		logger.SetLevel(logrus.ErrorLevel)
	case "fatal":
		logger.SetLevel(logrus.FatalLevel)
	default:
		logger.SetLevel(logrus.InfoLevel)
	}

	// Set formatter
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339,
		FieldMap: logrus.FieldMap{
			logrus.FieldKeyTime:  "timestamp",
			logrus.FieldKeyLevel: "level",
			logrus.FieldKeyMsg:   "message",
		},
	})

	// Set output
	logger.SetOutput(os.Stdout)

	return &Logger{
		Logger:      logger,
		serviceName: serviceName,
	}
}

// WithService adds service name to log entry
func (l *Logger) WithService() *logrus.Entry {
	return l.WithField("service", l.serviceName)
}

// WithRequestID adds request ID to log entry
func (l *Logger) WithRequestID(requestID string) *logrus.Entry {
	return l.WithService().WithField("request_id", requestID)
}

// WithUserID adds user ID to log entry
func (l *Logger) WithUserID(userID string) *logrus.Entry {
	return l.WithService().WithField("user_id", userID)
}

// WithError adds error to log entry
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.WithService().WithError(err)
}

// Info logs an info message
func (l *Logger) Info(msg string) {
	l.WithService().Info(msg)
}

// Debug logs a debug message
func (l *Logger) Debug(msg string) {
	l.WithService().Debug(msg)
}

// Warn logs a warning message
func (l *Logger) Warn(msg string) {
	l.WithService().Warn(msg)
}

// Error logs an error message
func (l *Logger) Error(msg string) {
	l.WithService().Error(msg)
}

// Fatal logs a fatal message and exits
func (l *Logger) Fatal(msg string) {
	l.WithService().Fatal(msg)
}

// InfoWithFields logs an info message with fields
func (l *Logger) InfoWithFields(msg string, fields logrus.Fields) {
	l.WithService().WithFields(fields).Info(msg)
}

// DebugWithFields logs a debug message with fields
func (l *Logger) DebugWithFields(msg string, fields logrus.Fields) {
	l.WithService().WithFields(fields).Debug(msg)
}

// WarnWithFields logs a warning message with fields
func (l *Logger) WarnWithFields(msg string, fields logrus.Fields) {
	l.WithService().WithFields(fields).Warn(msg)
}

// ErrorWithFields logs an error message with fields
func (l *Logger) ErrorWithFields(msg string, fields logrus.Fields) {
	l.WithService().WithFields(fields).Error(msg)
}

// FatalWithFields logs a fatal message with fields and exits
func (l *Logger) FatalWithFields(msg string, fields logrus.Fields) {
	l.WithService().WithFields(fields).Fatal(msg)
}
