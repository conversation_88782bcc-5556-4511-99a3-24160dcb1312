package common

import (
	"time"
	"github.com/google/uuid"
)

// ServiceInfo represents information about a service
type ServiceInfo struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Version     string    `json:"version"`
	Status      string    `json:"status"`
	StartedAt   time.Time `json:"started_at"`
	LastPing    time.Time `json:"last_ping"`
	Address     string    `json:"address"`
	Port        int       `json:"port"`
	HealthCheck string    `json:"health_check"`
}

// ProcessInfo represents information about a process
type ProcessInfo struct {
	ID          uuid.UUID         `json:"id"`
	ParentID    *uuid.UUID        `json:"parent_id,omitempty"`
	Name        string            `json:"name"`
	State       string            `json:"state"`
	Priority    int               `json:"priority"`
	CreatedAt   time.Time         `json:"created_at"`
	CPUTime     time.Duration     `json:"cpu_time"`
	Memory      uint64            `json:"memory"`
	Environment map[string]string `json:"environment"`
	WorkingDir  string            `json:"working_dir"`
	ExitCode    *int              `json:"exit_code,omitempty"`
}

// FileInfo represents file system information
type FileInfo struct {
	Name        string      `json:"name"`
	Path        string      `json:"path"`
	Size        int64       `json:"size"`
	Mode        uint32      `json:"mode"`
	ModTime     time.Time   `json:"mod_time"`
	IsDir       bool        `json:"is_dir"`
	Owner       string      `json:"owner"`
	Group       string      `json:"group"`
	Permissions string      `json:"permissions"`
}

// MemoryInfo represents memory usage information
type MemoryInfo struct {
	Total       uint64 `json:"total"`
	Used        uint64 `json:"used"`
	Free        uint64 `json:"free"`
	Cached      uint64 `json:"cached"`
	Buffers     uint64 `json:"buffers"`
	Available   uint64 `json:"available"`
	SwapTotal   uint64 `json:"swap_total"`
	SwapUsed    uint64 `json:"swap_used"`
	SwapFree    uint64 `json:"swap_free"`
}

// CPUInfo represents CPU usage information
type CPUInfo struct {
	Cores       int     `json:"cores"`
	Usage       float64 `json:"usage"`
	LoadAvg1    float64 `json:"load_avg_1"`
	LoadAvg5    float64 `json:"load_avg_5"`
	LoadAvg15   float64 `json:"load_avg_15"`
	UserTime    uint64  `json:"user_time"`
	SystemTime  uint64  `json:"system_time"`
	IdleTime    uint64  `json:"idle_time"`
}

// NetworkInfo represents network interface information
type NetworkInfo struct {
	Interface   string `json:"interface"`
	IPAddress   string `json:"ip_address"`
	Netmask     string `json:"netmask"`
	Gateway     string `json:"gateway"`
	MTU         int    `json:"mtu"`
	BytesRx     uint64 `json:"bytes_rx"`
	BytesTx     uint64 `json:"bytes_tx"`
	PacketsRx   uint64 `json:"packets_rx"`
	PacketsTx   uint64 `json:"packets_tx"`
	ErrorsRx    uint64 `json:"errors_rx"`
	ErrorsTx    uint64 `json:"errors_tx"`
}

// SystemStats represents overall system statistics
type SystemStats struct {
	Uptime      time.Duration `json:"uptime"`
	Processes   int           `json:"processes"`
	Memory      MemoryInfo    `json:"memory"`
	CPU         CPUInfo       `json:"cpu"`
	Network     []NetworkInfo `json:"network"`
	Filesystems []FileInfo    `json:"filesystems"`
	LoadAvg     [3]float64    `json:"load_avg"`
}

// Event represents a system event
type Event struct {
	ID        uuid.UUID              `json:"id"`
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
	Severity  string                 `json:"severity"`
}

// LogEntry represents a log entry
type LogEntry struct {
	ID        uuid.UUID              `json:"id"`
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Service   string                 `json:"service"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields"`
}

// Configuration represents service configuration
type Configuration struct {
	ServiceName string                 `json:"service_name"`
	Version     string                 `json:"version"`
	Debug       bool                   `json:"debug"`
	LogLevel    string                 `json:"log_level"`
	Address     string                 `json:"address"`
	Port        int                    `json:"port"`
	Database    DatabaseConfig         `json:"database"`
	Cache       CacheConfig            `json:"cache"`
	Security    SecurityConfig         `json:"security"`
	Custom      map[string]interface{} `json:"custom"`
}

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Driver   string `json:"driver"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Database string `json:"database"`
	Username string `json:"username"`
	Password string `json:"password"`
	SSLMode  string `json:"ssl_mode"`
}

// CacheConfig represents cache configuration
type CacheConfig struct {
	Driver   string `json:"driver"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Database int    `json:"database"`
	Password string `json:"password"`
	TTL      int    `json:"ttl"`
}

// SecurityConfig represents security configuration
type SecurityConfig struct {
	JWTSecret       string   `json:"jwt_secret"`
	AllowedOrigins  []string `json:"allowed_origins"`
	RateLimitRPS    int      `json:"rate_limit_rps"`
	EnableHTTPS     bool     `json:"enable_https"`
	CertFile        string   `json:"cert_file"`
	KeyFile         string   `json:"key_file"`
}

// Response represents a standard API response
type Response struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Code    int         `json:"code"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code"`
	Details string `json:"details,omitempty"`
}

// HealthStatus represents service health status
type HealthStatus struct {
	Status      string            `json:"status"`
	Timestamp   time.Time         `json:"timestamp"`
	Version     string            `json:"version"`
	Uptime      time.Duration     `json:"uptime"`
	Checks      map[string]string `json:"checks"`
	Dependencies []string         `json:"dependencies"`
}

// Constants for common values
const (
	// Service statuses
	StatusStarting = "starting"
	StatusRunning  = "running"
	StatusStopping = "stopping"
	StatusStopped  = "stopped"
	StatusError    = "error"

	// Process states
	ProcessStateCreated    = "created"
	ProcessStateReady      = "ready"
	ProcessStateRunning    = "running"
	ProcessStateBlocked    = "blocked"
	ProcessStateTerminated = "terminated"

	// Event types
	EventTypeProcess    = "process"
	EventTypeMemory     = "memory"
	EventTypeNetwork    = "network"
	EventTypeFilesystem = "filesystem"
	EventTypeSystem     = "system"
	EventTypeAuth       = "auth"

	// Event severities
	SeverityInfo    = "info"
	SeverityWarning = "warning"
	SeverityError   = "error"
	SeverityCritical = "critical"

	// Log levels
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
	LogLevelFatal = "fatal"
)
