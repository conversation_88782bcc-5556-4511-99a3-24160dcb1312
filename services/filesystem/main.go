package main

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"distributed-kernel/services/common"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

// FilesystemService implements the distributed filesystem
type FilesystemService struct {
	logger    *common.Logger
	id        uuid.UUID
	startTime time.Time
	files     map[string]*VirtualFile
	dirs      map[string]*VirtualDirectory
}

// VirtualFile represents a file in the virtual filesystem
type VirtualFile struct {
	ID          uuid.UUID         `json:"id"`
	Name        string            `json:"name"`
	Path        string            `json:"path"`
	Size        int64             `json:"size"`
	Content     []byte            `json:"content"`
	Mode        uint32            `json:"mode"`
	Owner       string            `json:"owner"`
	Group       string            `json:"group"`
	CreatedAt   time.Time         `json:"created_at"`
	ModifiedAt  time.Time         `json:"modified_at"`
	AccessedAt  time.Time         `json:"accessed_at"`
	Metadata    map[string]string `json:"metadata"`
}

// VirtualDirectory represents a directory in the virtual filesystem
type VirtualDirectory struct {
	ID         uuid.UUID         `json:"id"`
	Name       string            `json:"name"`
	Path       string            `json:"path"`
	Mode       uint32            `json:"mode"`
	Owner      string            `json:"owner"`
	Group      string            `json:"group"`
	CreatedAt  time.Time         `json:"created_at"`
	ModifiedAt time.Time         `json:"modified_at"`
	AccessedAt time.Time         `json:"accessed_at"`
	Children   []string          `json:"children"`
	Metadata   map[string]string `json:"metadata"`
}

// NewFilesystemService creates a new filesystem service
func NewFilesystemService() *FilesystemService {
	logger := common.NewLogger("filesystem", "info")
	
	fs := &FilesystemService{
		logger:    logger,
		id:        uuid.New(),
		startTime: time.Now(),
		files:     make(map[string]*VirtualFile),
		dirs:      make(map[string]*VirtualDirectory),
	}
	
	// Initialize root directory
	fs.initializeRootFS()
	
	return fs
}

// initializeRootFS sets up the basic filesystem structure
func (fs *FilesystemService) initializeRootFS() {
	now := time.Now()
	
	// Create root directory
	rootDir := &VirtualDirectory{
		ID:         uuid.New(),
		Name:       "/",
		Path:       "/",
		Mode:       0755,
		Owner:      "root",
		Group:      "root",
		CreatedAt:  now,
		ModifiedAt: now,
		AccessedAt: now,
		Children:   []string{},
		Metadata:   make(map[string]string),
	}
	fs.dirs["/"] = rootDir
	
	// Create standard directories
	standardDirs := []string{"/bin", "/usr", "/usr/bin", "/home", "/tmp", "/var", "/etc"}
	for _, dir := range standardDirs {
		fs.createDirectory(dir, "root", "root", 0755)
	}
	
	// Create some sample files
	fs.createFile("/etc/hostname", []byte("distributed-kernel"), "root", "root", 0644)
	fs.createFile("/etc/version", []byte("1.0.0"), "root", "root", 0644)
	fs.createFile("/tmp/welcome.txt", []byte("Welcome to the Distributed Kernel!"), "root", "root", 0644)
	
	fs.logger.Info("Initialized root filesystem")
}

// createDirectory creates a new directory
func (fs *FilesystemService) createDirectory(path, owner, group string, mode uint32) error {
	now := time.Now()
	
	dir := &VirtualDirectory{
		ID:         uuid.New(),
		Name:       getBaseName(path),
		Path:       path,
		Mode:       mode,
		Owner:      owner,
		Group:      group,
		CreatedAt:  now,
		ModifiedAt: now,
		AccessedAt: now,
		Children:   []string{},
		Metadata:   make(map[string]string),
	}
	
	fs.dirs[path] = dir
	
	// Add to parent directory
	parentPath := getParentPath(path)
	if parentDir, exists := fs.dirs[parentPath]; exists {
		parentDir.Children = append(parentDir.Children, path)
		parentDir.ModifiedAt = now
	}
	
	fs.logger.InfoWithFields("Created directory", logrus.Fields{
		"path":  path,
		"owner": owner,
		"mode":  fmt.Sprintf("%o", mode),
	})
	
	return nil
}

// createFile creates a new file
func (fs *FilesystemService) createFile(path string, content []byte, owner, group string, mode uint32) error {
	now := time.Now()
	
	file := &VirtualFile{
		ID:         uuid.New(),
		Name:       getBaseName(path),
		Path:       path,
		Size:       int64(len(content)),
		Content:    content,
		Mode:       mode,
		Owner:      owner,
		Group:      group,
		CreatedAt:  now,
		ModifiedAt: now,
		AccessedAt: now,
		Metadata:   make(map[string]string),
	}
	
	fs.files[path] = file
	
	// Add to parent directory
	parentPath := getParentPath(path)
	if parentDir, exists := fs.dirs[parentPath]; exists {
		parentDir.Children = append(parentDir.Children, path)
		parentDir.ModifiedAt = now
	}
	
	fs.logger.InfoWithFields("Created file", logrus.Fields{
		"path":  path,
		"size":  len(content),
		"owner": owner,
		"mode":  fmt.Sprintf("%o", mode),
	})
	
	return nil
}

// ReadFile reads a file from the filesystem
func (fs *FilesystemService) ReadFile(path string) (*VirtualFile, error) {
	file, exists := fs.files[path]
	if !exists {
		return nil, fmt.Errorf("file not found: %s", path)
	}
	
	// Update access time
	file.AccessedAt = time.Now()
	
	fs.logger.DebugWithFields("Read file", logrus.Fields{
		"path": path,
		"size": file.Size,
	})
	
	return file, nil
}

// WriteFile writes content to a file
func (fs *FilesystemService) WriteFile(path string, content []byte, owner, group string, mode uint32) error {
	now := time.Now()
	
	if file, exists := fs.files[path]; exists {
		// Update existing file
		file.Content = content
		file.Size = int64(len(content))
		file.ModifiedAt = now
		file.AccessedAt = now
	} else {
		// Create new file
		return fs.createFile(path, content, owner, group, mode)
	}
	
	fs.logger.InfoWithFields("Wrote file", logrus.Fields{
		"path": path,
		"size": len(content),
	})
	
	return nil
}

// ListDirectory lists the contents of a directory
func (fs *FilesystemService) ListDirectory(path string) ([]common.FileInfo, error) {
	dir, exists := fs.dirs[path]
	if !exists {
		return nil, fmt.Errorf("directory not found: %s", path)
	}
	
	// Update access time
	dir.AccessedAt = time.Now()
	
	var files []common.FileInfo
	
	for _, childPath := range dir.Children {
		if file, exists := fs.files[childPath]; exists {
			files = append(files, common.FileInfo{
				Name:        file.Name,
				Path:        file.Path,
				Size:        file.Size,
				Mode:        file.Mode,
				ModTime:     file.ModifiedAt,
				IsDir:       false,
				Owner:       file.Owner,
				Group:       file.Group,
				Permissions: fmt.Sprintf("%o", file.Mode),
			})
		} else if subdir, exists := fs.dirs[childPath]; exists {
			files = append(files, common.FileInfo{
				Name:        subdir.Name,
				Path:        subdir.Path,
				Size:        0,
				Mode:        subdir.Mode,
				ModTime:     subdir.ModifiedAt,
				IsDir:       true,
				Owner:       subdir.Owner,
				Group:       subdir.Group,
				Permissions: fmt.Sprintf("%o", subdir.Mode),
			})
		}
	}
	
	fs.logger.DebugWithFields("Listed directory", logrus.Fields{
		"path":  path,
		"count": len(files),
	})
	
	return files, nil
}

// DeleteFile deletes a file
func (fs *FilesystemService) DeleteFile(path string) error {
	if _, exists := fs.files[path]; !exists {
		return fmt.Errorf("file not found: %s", path)
	}
	
	delete(fs.files, path)
	
	// Remove from parent directory
	parentPath := getParentPath(path)
	if parentDir, exists := fs.dirs[parentPath]; exists {
		for i, child := range parentDir.Children {
			if child == path {
				parentDir.Children = append(parentDir.Children[:i], parentDir.Children[i+1:]...)
				parentDir.ModifiedAt = time.Now()
				break
			}
		}
	}
	
	fs.logger.InfoWithFields("Deleted file", logrus.Fields{
		"path": path,
	})
	
	return nil
}

// GetStats returns filesystem statistics
func (fs *FilesystemService) GetStats() map[string]interface{} {
	totalFiles := len(fs.files)
	totalDirs := len(fs.dirs)
	totalSize := int64(0)
	
	for _, file := range fs.files {
		totalSize += file.Size
	}
	
	return map[string]interface{}{
		"total_files":       totalFiles,
		"total_directories": totalDirs,
		"total_size":        totalSize,
		"uptime":            time.Since(fs.startTime).Seconds(),
	}
}

// Helper functions
func getBaseName(path string) string {
	if path == "/" {
		return "/"
	}
	
	for i := len(path) - 1; i >= 0; i-- {
		if path[i] == '/' {
			return path[i+1:]
		}
	}
	return path
}

func getParentPath(path string) string {
	if path == "/" {
		return "/"
	}
	
	for i := len(path) - 1; i >= 0; i-- {
		if path[i] == '/' {
			if i == 0 {
				return "/"
			}
			return path[:i]
		}
	}
	return "/"
}

func main() {
	fs := NewFilesystemService()
	
	// Create gRPC server
	lis, err := net.Listen("tcp", ":50051")
	if err != nil {
		fs.logger.FatalWithFields("Failed to listen", logrus.Fields{"error": err})
	}
	
	s := grpc.NewServer()
	
	fs.logger.Info("Filesystem service starting on :50051")
	
	// Handle graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	
	go func() {
		<-c
		fs.logger.Info("Shutting down filesystem service...")
		s.GracefulStop()
	}()
	
	if err := s.Serve(lis); err != nil {
		fs.logger.FatalWithFields("Failed to serve", logrus.Fields{"error": err})
	}
}
