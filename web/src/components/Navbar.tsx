import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Chip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Terminal as TerminalIcon,
  List as ProcessIcon,
  Folder as FileIcon,
  Monitor as SystemIcon,
  NetworkCheck as NetworkIcon,
} from '@mui/icons-material';

const Navbar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    { path: '/', label: 'Dashboard', icon: <DashboardIcon /> },
    { path: '/terminal', label: 'Terminal', icon: <TerminalIcon /> },
    { path: '/processes', label: 'Processes', icon: <ProcessIcon /> },
    { path: '/files', label: 'Files', icon: <FileIcon /> },
    { path: '/system', label: 'System', icon: <SystemIcon /> },
    { path: '/network', label: 'Network', icon: <NetworkIcon /> },
  ];

  return (
    <AppBar position="static" elevation={0}>
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h5" sx={{ color: '#00ff41', fontWeight: 'bold' }}>
              DistributedKernel
            </Typography>
            <Chip 
              label="v1.0.0" 
              size="small" 
              sx={{ 
                backgroundColor: '#00ff41', 
                color: '#000000',
                fontFamily: 'monospace',
                fontSize: '0.7rem'
              }} 
            />
          </Box>
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          {menuItems.map((item) => (
            <Button
              key={item.path}
              color="inherit"
              startIcon={item.icon}
              onClick={() => navigate(item.path)}
              sx={{
                color: location.pathname === item.path ? '#00ff41' : '#ffffff',
                backgroundColor: location.pathname === item.path ? 'rgba(0, 255, 65, 0.1)' : 'transparent',
                border: location.pathname === item.path ? '1px solid #00ff41' : '1px solid transparent',
                '&:hover': {
                  backgroundColor: 'rgba(0, 255, 65, 0.1)',
                  border: '1px solid #00ff41',
                },
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
