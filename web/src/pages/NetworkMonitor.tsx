import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  LinearProgress,
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';

interface NetworkInterface {
  name: string;
  ip_address: string;
  netmask: string;
  gateway: string;
  mtu: number;
  status: 'up' | 'down';
  bytes_rx: number;
  bytes_tx: number;
  packets_rx: number;
  packets_tx: number;
  errors_rx: number;
  errors_tx: number;
}

interface Connection {
  id: string;
  local_addr: string;
  remote_addr: string;
  state: string;
  protocol: string;
  bytes_sent: number;
  bytes_recv: number;
}

interface NetworkMetrics {
  timestamp: string;
  bytes_rx: number;
  bytes_tx: number;
  packets_rx: number;
  packets_tx: number;
  errors: number;
}

const NetworkMonitor: React.FC = () => {
  const [interfaces, setInterfaces] = useState<NetworkInterface[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [metrics, setMetrics] = useState<NetworkMetrics[]>([]);
  const [totalStats, setTotalStats] = useState({
    total_bytes_rx: 0,
    total_bytes_tx: 0,
    total_packets_rx: 0,
    total_packets_tx: 0,
    active_connections: 0,
    interfaces_up: 0,
  });

  useEffect(() => {
    // Initialize network interfaces
    const mockInterfaces: NetworkInterface[] = [
      {
        name: 'lo',
        ip_address: '127.0.0.1',
        netmask: '*********',
        gateway: '',
        mtu: 65536,
        status: 'up',
        bytes_rx: 1024000,
        bytes_tx: 1024000,
        packets_rx: 5000,
        packets_tx: 5000,
        errors_rx: 0,
        errors_tx: 0,
      },
      {
        name: 'eth0',
        ip_address: '*************',
        netmask: '*************',
        gateway: '***********',
        mtu: 1500,
        status: 'up',
        bytes_rx: 50000000,
        bytes_tx: 25000000,
        packets_rx: 75000,
        packets_tx: 50000,
        errors_rx: 2,
        errors_tx: 1,
      },
    ];

    setInterfaces(mockInterfaces);

    // Initialize connections
    const mockConnections: Connection[] = [
      {
        id: '1',
        local_addr: '*************:8080',
        remote_addr: '************:45678',
        state: 'ESTABLISHED',
        protocol: 'TCP',
        bytes_sent: 15000,
        bytes_recv: 8000,
      },
      {
        id: '2',
        local_addr: '*************:22',
        remote_addr: '************:54321',
        state: 'ESTABLISHED',
        protocol: 'TCP',
        bytes_sent: 5000,
        bytes_recv: 12000,
      },
      {
        id: '3',
        local_addr: '*************:3000',
        remote_addr: '************:33445',
        state: 'TIME_WAIT',
        protocol: 'TCP',
        bytes_sent: 2000,
        bytes_recv: 1500,
      },
    ];

    setConnections(mockConnections);

    // Start real-time updates
    const interval = setInterval(() => {
      const now = new Date();
      const timeStr = now.toLocaleTimeString();

      // Update interface statistics
      setInterfaces(prev => prev.map(iface => ({
        ...iface,
        bytes_rx: iface.bytes_rx + Math.floor(Math.random() * 10000),
        bytes_tx: iface.bytes_tx + Math.floor(Math.random() * 8000),
        packets_rx: iface.packets_rx + Math.floor(Math.random() * 100),
        packets_tx: iface.packets_tx + Math.floor(Math.random() * 80),
        errors_rx: iface.errors_rx + (Math.random() < 0.01 ? 1 : 0),
        errors_tx: iface.errors_tx + (Math.random() < 0.01 ? 1 : 0),
      })));

      // Update metrics history
      const newMetric: NetworkMetrics = {
        timestamp: timeStr,
        bytes_rx: Math.floor(Math.random() * 50000),
        bytes_tx: Math.floor(Math.random() * 30000),
        packets_rx: Math.floor(Math.random() * 500),
        packets_tx: Math.floor(Math.random() * 300),
        errors: Math.floor(Math.random() * 3),
      };

      setMetrics(prev => {
        const updated = [...prev, newMetric];
        return updated.slice(-20); // Keep last 20 points
      });

      // Update total stats
      setTotalStats(prev => ({
        total_bytes_rx: prev.total_bytes_rx + newMetric.bytes_rx,
        total_bytes_tx: prev.total_bytes_tx + newMetric.bytes_tx,
        total_packets_rx: prev.total_packets_rx + newMetric.packets_rx,
        total_packets_tx: prev.total_packets_tx + newMetric.packets_tx,
        active_connections: mockConnections.filter(c => c.state === 'ESTABLISHED').length,
        interfaces_up: mockInterfaces.filter(i => i.status === 'up').length,
      }));

      // Simulate connection changes
      if (Math.random() < 0.1) {
        setConnections(prev => {
          const updated = [...prev];
          if (updated.length > 0) {
            const randomIndex = Math.floor(Math.random() * updated.length);
            updated[randomIndex] = {
              ...updated[randomIndex],
              bytes_sent: updated[randomIndex].bytes_sent + Math.floor(Math.random() * 1000),
              bytes_recv: updated[randomIndex].bytes_recv + Math.floor(Math.random() * 1000),
            };
          }
          return updated;
        });
      }
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getConnectionStateColor = (state: string) => {
    switch (state) {
      case 'ESTABLISHED': return '#00ff41';
      case 'LISTEN': return '#ffff00';
      case 'TIME_WAIT': return '#ff6b35';
      case 'CLOSE_WAIT': return '#ff6b35';
      default: return '#ccc';
    }
  };

  const getInterfaceUtilization = (iface: NetworkInterface): number => {
    // Calculate utilization based on MTU and current traffic
    const maxThroughput = iface.mtu * 1000; // Simplified calculation
    const currentThroughput = (iface.bytes_rx + iface.bytes_tx) / 1000;
    return Math.min(100, (currentThroughput / maxThroughput) * 100);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, color: '#00ff41' }}>
        Network Monitor
      </Typography>

      <Grid container spacing={3}>
        {/* Network Overview Cards */}
        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#00ff41', mb: 1 }}>
                Total RX
              </Typography>
              <Typography variant="h4" sx={{ color: '#00ff41' }}>
                {formatBytes(totalStats.total_bytes_rx)}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                {totalStats.total_packets_rx.toLocaleString()} packets
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#ff6b35', mb: 1 }}>
                Total TX
              </Typography>
              <Typography variant="h4" sx={{ color: '#ff6b35' }}>
                {formatBytes(totalStats.total_bytes_tx)}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                {totalStats.total_packets_tx.toLocaleString()} packets
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#00ff41', mb: 1 }}>
                Active Connections
              </Typography>
              <Typography variant="h4" sx={{ color: '#00ff41' }}>
                {totalStats.active_connections}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                {connections.length} total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#00ff41', mb: 1 }}>
                Interfaces
              </Typography>
              <Typography variant="h4" sx={{ color: '#00ff41' }}>
                {totalStats.interfaces_up}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                {interfaces.length} total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Network Traffic Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Network Traffic History
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={metrics}>
                <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                <XAxis dataKey="timestamp" stroke="#ccc" />
                <YAxis stroke="#ccc" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1a1a1a',
                    border: '1px solid #00ff41',
                    color: '#fff'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="bytes_rx"
                  stroke="#00ff41"
                  strokeWidth={2}
                  dot={false}
                  name="RX (bytes/s)"
                />
                <Line
                  type="monotone"
                  dataKey="bytes_tx"
                  stroke="#ff6b35"
                  strokeWidth={2}
                  dot={false}
                  name="TX (bytes/s)"
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Packet Statistics */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Packet Statistics
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={metrics.slice(-10)}>
                <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                <XAxis dataKey="timestamp" stroke="#ccc" />
                <YAxis stroke="#ccc" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1a1a1a',
                    border: '1px solid #00ff41',
                    color: '#fff'
                  }}
                />
                <Bar dataKey="packets_rx" fill="#00ff41" name="RX Packets" />
                <Bar dataKey="packets_tx" fill="#ff6b35" name="TX Packets" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Network Interfaces */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Network Interfaces
            </Typography>
            <List>
              {interfaces.map((iface) => (
                <ListItem key={iface.name} sx={{ border: '1px solid #333', mb: 1, borderRadius: 1 }}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="h6" sx={{ color: '#fff' }}>
                          {iface.name}
                        </Typography>
                        <Chip
                          label={iface.status}
                          size="small"
                          sx={{
                            backgroundColor: iface.status === 'up' ? '#00ff41' : '#ff6b35',
                            color: '#000',
                            fontWeight: 'bold',
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" sx={{ color: '#ccc' }}>
                          IP: {iface.ip_address} | MTU: {iface.mtu}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#ccc' }}>
                          RX: {formatBytes(iface.bytes_rx)} | TX: {formatBytes(iface.bytes_tx)}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#ccc', mb: 1 }}>
                          Errors: RX {iface.errors_rx} | TX {iface.errors_tx}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" sx={{ color: '#ccc' }}>
                            Utilization:
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={getInterfaceUtilization(iface)}
                            sx={{
                              flex: 1,
                              backgroundColor: '#333',
                              '& .MuiLinearProgress-bar': { backgroundColor: '#00ff41' }
                            }}
                          />
                          <Typography variant="body2" sx={{ color: '#ccc' }}>
                            {getInterfaceUtilization(iface).toFixed(1)}%
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Active Connections */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Active Connections
            </Typography>
            <TableContainer sx={{ maxHeight: 400 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Protocol</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Local</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Remote</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>State</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Traffic</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {connections.map((conn) => (
                    <TableRow key={conn.id}>
                      <TableCell sx={{ color: '#fff', fontFamily: 'monospace' }}>
                        {conn.protocol}
                      </TableCell>
                      <TableCell sx={{ color: '#fff', fontFamily: 'monospace', fontSize: '0.8rem' }}>
                        {conn.local_addr}
                      </TableCell>
                      <TableCell sx={{ color: '#fff', fontFamily: 'monospace', fontSize: '0.8rem' }}>
                        {conn.remote_addr}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={conn.state}
                          size="small"
                          sx={{
                            backgroundColor: getConnectionStateColor(conn.state),
                            color: '#000',
                            fontWeight: 'bold',
                            fontSize: '0.7rem',
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ color: '#ccc', fontSize: '0.8rem' }}>
                        ↑{formatBytes(conn.bytes_sent)} ↓{formatBytes(conn.bytes_recv)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default NetworkMonitor;
