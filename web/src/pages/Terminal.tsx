import React, { useEffect, useRef, useState } from 'react';
import { Terminal as XTerm } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { Box, Paper, Typography } from '@mui/material';
import io, { Socket } from 'socket.io-client';
import 'xterm/css/xterm.css';

const Terminal: React.FC = () => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<XTerm | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Initialize xterm.js
    const terminal = new XTerm({
      theme: {
        background: '#0a0a0a',
        foreground: '#00ff41',
        cursor: '#00ff41',
        cursorAccent: '#000000',
        selection: 'rgba(0, 255, 65, 0.3)',
        black: '#000000',
        red: '#ff6b35',
        green: '#00ff41',
        yellow: '#ffff00',
        blue: '#0080ff',
        magenta: '#ff00ff',
        cyan: '#00ffff',
        white: '#ffffff',
        brightBlack: '#808080',
        brightRed: '#ff8080',
        brightGreen: '#80ff80',
        brightYellow: '#ffff80',
        brightBlue: '#8080ff',
        brightMagenta: '#ff80ff',
        brightCyan: '#80ffff',
        brightWhite: '#ffffff',
      },
      fontFamily: '"Fira Code", "Courier New", monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 1000,
      tabStopWidth: 4,
    });

    // Add addons
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    
    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);

    // Open terminal
    terminal.open(terminalRef.current);
    fitAddon.fit();

    // Store reference
    xtermRef.current = terminal;

    // Connect to WebSocket
    const socket = io('ws://localhost:8080', {
      transports: ['websocket'],
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      setIsConnected(true);
      terminal.writeln('\x1b[32m✓ Connected to Distributed Kernel\x1b[0m');
      terminal.writeln('\x1b[36mWelcome to the Distributed Operating System!\x1b[0m');
      terminal.writeln('\x1b[33mType "help" for available commands.\x1b[0m');
      terminal.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
      terminal.writeln('\x1b[31m✗ Disconnected from kernel\x1b[0m');
    });

    socket.on('output', (data: string) => {
      terminal.write(data);
    });

    socket.on('command_result', (result: any) => {
      if (result.success) {
        if (result.output) {
          terminal.writeln(result.output);
        }
      } else {
        terminal.writeln(`\x1b[31mError: ${result.error}\x1b[0m`);
      }
      terminal.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
    });

    // Handle user input
    let currentLine = '';
    terminal.onData((data) => {
      const code = data.charCodeAt(0);
      
      if (code === 13) { // Enter
        terminal.writeln('');
        if (currentLine.trim()) {
          handleCommand(currentLine.trim());
        } else {
          terminal.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
        }
        currentLine = '';
      } else if (code === 127) { // Backspace
        if (currentLine.length > 0) {
          currentLine = currentLine.slice(0, -1);
          terminal.write('\b \b');
        }
      } else if (code >= 32) { // Printable characters
        currentLine += data;
        terminal.write(data);
      }
    });

    // Handle window resize
    const handleResize = () => {
      fitAddon.fit();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      terminal.dispose();
      socket.disconnect();
    };
  }, []);

  const handleCommand = (command: string) => {
    if (!socketRef.current) return;

    // Handle built-in commands
    switch (command.toLowerCase()) {
      case 'help':
        const helpText = `
\x1b[36mAvailable Commands:\x1b[0m
  help          - Show this help message
  ps            - List running processes
  ls [path]     - List directory contents
  cat <file>    - Display file contents
  mkdir <dir>   - Create directory
  touch <file>  - Create empty file
  rm <file>     - Remove file
  top           - Show system resources
  netstat       - Show network connections
  whoami        - Show current user
  uname         - Show system information
  date          - Show current date/time
  clear         - Clear terminal
  exit          - Exit terminal session
`;
        xtermRef.current?.writeln(helpText);
        xtermRef.current?.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
        break;

      case 'clear':
        xtermRef.current?.clear();
        xtermRef.current?.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
        break;

      case 'whoami':
        xtermRef.current?.writeln('root');
        xtermRef.current?.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
        break;

      case 'uname':
        xtermRef.current?.writeln('DistributedKernel 1.0.0 x86_64');
        xtermRef.current?.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
        break;

      case 'date':
        xtermRef.current?.writeln(new Date().toString());
        xtermRef.current?.write('\x1b[32mkernel@distributed:~$ \x1b[0m');
        break;

      case 'exit':
        xtermRef.current?.writeln('Goodbye!');
        socketRef.current?.disconnect();
        break;

      default:
        // Send command to kernel
        socketRef.current.emit('command', { command });
        break;
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2, borderBottom: '1px solid #333' }}>
        <Typography variant="h5" sx={{ color: '#00ff41', mb: 1 }}>
          Terminal
        </Typography>
        <Typography variant="body2" sx={{ color: isConnected ? '#00ff41' : '#ff6b35' }}>
          Status: {isConnected ? 'Connected' : 'Disconnected'}
        </Typography>
      </Box>
      
      <Paper 
        sx={{ 
          flex: 1, 
          m: 2, 
          backgroundColor: '#0a0a0a',
          border: '1px solid #00ff41',
          borderRadius: 1,
          overflow: 'hidden'
        }}
      >
        <div 
          ref={terminalRef} 
          style={{ 
            height: '100%', 
            padding: '10px',
            backgroundColor: '#0a0a0a'
          }} 
        />
      </Paper>
    </Box>
  );
};

export default Terminal;
