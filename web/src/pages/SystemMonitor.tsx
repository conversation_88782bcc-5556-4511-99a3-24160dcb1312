import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Card,
  CardContent,
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

interface SystemMetrics {
  timestamp: string;
  cpu: number;
  memory: number;
  disk: number;
  network_rx: number;
  network_tx: number;
}

interface ProcessInfo {
  pid: string;
  name: string;
  cpu_percent: number;
  memory_mb: number;
  state: string;
}

const SystemMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics[]>([]);
  const [processes, setProcesses] = useState<ProcessInfo[]>([]);
  const [currentStats, setCurrentStats] = useState({
    cpu: 0,
    memory: 0,
    disk: 30,
    network: 0,
    uptime: 0,
    load_avg: [0.15, 0.12, 0.08],
  });

  useEffect(() => {
    // Simulate real-time metrics
    const interval = setInterval(() => {
      const now = new Date();
      const timeStr = now.toLocaleTimeString();
      
      const newMetric: SystemMetrics = {
        timestamp: timeStr,
        cpu: Math.random() * 80 + 10,
        memory: Math.random() * 60 + 20,
        disk: 30 + Math.random() * 10,
        network_rx: Math.random() * 1000,
        network_tx: Math.random() * 800,
      };

      setMetrics(prev => {
        const updated = [...prev, newMetric];
        return updated.slice(-20); // Keep last 20 points
      });

      setCurrentStats({
        cpu: newMetric.cpu,
        memory: newMetric.memory,
        disk: newMetric.disk,
        network: (newMetric.network_rx + newMetric.network_tx) / 100,
        uptime: Math.floor(Date.now() / 1000) - 3600,
        load_avg: [
          Math.random() * 2,
          Math.random() * 1.8,
          Math.random() * 1.5,
        ],
      });

      // Update process list
      setProcesses([
        {
          pid: '1',
          name: 'kernel',
          cpu_percent: Math.random() * 10,
          memory_mb: 128 + Math.floor(Math.random() * 50),
          state: 'running',
        },
        {
          pid: '2',
          name: 'filesystem-service',
          cpu_percent: Math.random() * 5,
          memory_mb: 64 + Math.floor(Math.random() * 30),
          state: 'running',
        },
        {
          pid: '3',
          name: 'network-service',
          cpu_percent: Math.random() * 3,
          memory_mb: 48 + Math.floor(Math.random() * 20),
          state: 'running',
        },
        {
          pid: '4',
          name: 'web-bridge',
          cpu_percent: Math.random() * 4,
          memory_mb: 96 + Math.floor(Math.random() * 40),
          state: 'running',
        },
        {
          pid: '5',
          name: 'user-shell',
          cpu_percent: Math.random() * 1,
          memory_mb: 16 + Math.floor(Math.random() * 10),
          state: 'sleeping',
        },
      ]);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const formatUptime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'running': return '#00ff41';
      case 'sleeping': return '#ffff00';
      case 'stopped': return '#ff6b35';
      default: return '#ccc';
    }
  };

  const pieData = [
    { name: 'Used', value: currentStats.memory, color: '#00ff41' },
    { name: 'Free', value: 100 - currentStats.memory, color: '#333' },
  ];

  const diskData = [
    { name: 'Used', value: currentStats.disk, color: '#ff6b35' },
    { name: 'Free', value: 100 - currentStats.disk, color: '#333' },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, color: '#00ff41' }}>
        System Monitor
      </Typography>

      <Grid container spacing={3}>
        {/* Real-time Metrics */}
        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#00ff41', mb: 2 }}>
                CPU Usage
              </Typography>
              <Typography variant="h3" sx={{ color: '#00ff41', mb: 1 }}>
                {currentStats.cpu.toFixed(1)}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={currentStats.cpu}
                sx={{
                  backgroundColor: '#333',
                  '& .MuiLinearProgress-bar': { backgroundColor: '#00ff41' }
                }}
              />
              <Typography variant="body2" sx={{ mt: 1, color: '#ccc' }}>
                Load: {currentStats.load_avg.map(l => l.toFixed(2)).join(', ')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#00ff41', mb: 2 }}>
                Memory Usage
              </Typography>
              <Typography variant="h3" sx={{ color: '#00ff41', mb: 1 }}>
                {currentStats.memory.toFixed(1)}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={currentStats.memory}
                sx={{
                  backgroundColor: '#333',
                  '& .MuiLinearProgress-bar': { backgroundColor: '#00ff41' }
                }}
              />
              <Typography variant="body2" sx={{ mt: 1, color: '#ccc' }}>
                512MB / 1GB
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#00ff41', mb: 2 }}>
                Disk Usage
              </Typography>
              <Typography variant="h3" sx={{ color: '#ff6b35', mb: 1 }}>
                {currentStats.disk.toFixed(1)}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={currentStats.disk}
                sx={{
                  backgroundColor: '#333',
                  '& .MuiLinearProgress-bar': { backgroundColor: '#ff6b35' }
                }}
              />
              <Typography variant="body2" sx={{ mt: 1, color: '#ccc' }}>
                3GB / 10GB
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: '#00ff41', mb: 2 }}>
                System Info
              </Typography>
              <Typography variant="body1" sx={{ color: '#fff', mb: 1 }}>
                Uptime: {formatUptime(currentStats.uptime)}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc', mb: 1 }}>
                Processes: {processes.length}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Kernel: v1.0.0
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* CPU History Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              CPU & Memory Usage History
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={metrics}>
                <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                <XAxis dataKey="timestamp" stroke="#ccc" />
                <YAxis stroke="#ccc" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1a1a1a',
                    border: '1px solid #00ff41',
                    color: '#fff'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="cpu"
                  stroke="#00ff41"
                  strokeWidth={2}
                  dot={false}
                  name="CPU %"
                />
                <Line
                  type="monotone"
                  dataKey="memory"
                  stroke="#ff6b35"
                  strokeWidth={2}
                  dot={false}
                  name="Memory %"
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Memory Distribution */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Memory Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1a1a1a',
                    border: '1px solid #00ff41',
                    color: '#fff'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Network Activity */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Network Activity
            </Typography>
            <ResponsiveContainer width="100%" height={200}>
              <AreaChart data={metrics}>
                <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                <XAxis dataKey="timestamp" stroke="#ccc" />
                <YAxis stroke="#ccc" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1a1a1a',
                    border: '1px solid #00ff41',
                    color: '#fff'
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="network_rx"
                  stackId="1"
                  stroke="#00ff41"
                  fill="#00ff41"
                  fillOpacity={0.3}
                  name="RX (KB/s)"
                />
                <Area
                  type="monotone"
                  dataKey="network_tx"
                  stackId="1"
                  stroke="#ff6b35"
                  fill="#ff6b35"
                  fillOpacity={0.3}
                  name="TX (KB/s)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Top Processes */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Top Processes
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>PID</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Name</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>CPU%</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Memory</TableCell>
                    <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>State</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {processes
                    .sort((a, b) => b.cpu_percent - a.cpu_percent)
                    .map((process) => (
                      <TableRow key={process.pid}>
                        <TableCell sx={{ color: '#fff', fontFamily: 'monospace' }}>
                          {process.pid}
                        </TableCell>
                        <TableCell sx={{ color: '#fff' }}>
                          {process.name}
                        </TableCell>
                        <TableCell sx={{ color: '#fff', fontFamily: 'monospace' }}>
                          {process.cpu_percent.toFixed(1)}%
                        </TableCell>
                        <TableCell sx={{ color: '#fff', fontFamily: 'monospace' }}>
                          {process.memory_mb}MB
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={process.state}
                            size="small"
                            sx={{
                              backgroundColor: getStateColor(process.state),
                              color: '#000',
                              fontWeight: 'bold',
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemMonitor;
