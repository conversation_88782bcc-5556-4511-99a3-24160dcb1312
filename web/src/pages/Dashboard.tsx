import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Speed as CpuIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  Computer as ProcessIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface SystemStats {
  memory: {
    total: number;
    used: number;
    free: number;
    usage_percent: number;
  };
  cpu: {
    usage_percent: number;
    cores: number;
    load_avg: number[];
  };
  processes: {
    total: number;
    running: number;
    sleeping: number;
  };
  network: {
    interfaces: number;
    connections: number;
    bytes_rx: number;
    bytes_tx: number;
  };
  uptime: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [cpuHistory, setCpuHistory] = useState<Array<{ time: string; cpu: number }>>([]);
  const [memoryHistory, setMemoryHistory] = useState<Array<{ time: string; memory: number }>>([]);

  useEffect(() => {
    // Simulate real-time data updates
    const interval = setInterval(() => {
      const now = new Date();
      const timeStr = now.toLocaleTimeString();
      
      // Generate mock system stats
      const mockStats: SystemStats = {
        memory: {
          total: 1024 * 1024 * 1024, // 1GB
          used: Math.floor(Math.random() * 512 * 1024 * 1024) + 256 * 1024 * 1024, // 256MB - 768MB
          free: 0,
          usage_percent: 0,
        },
        cpu: {
          usage_percent: Math.floor(Math.random() * 80) + 10, // 10-90%
          cores: 4,
          load_avg: [
            Math.random() * 2,
            Math.random() * 2,
            Math.random() * 2,
          ],
        },
        processes: {
          total: Math.floor(Math.random() * 50) + 20,
          running: Math.floor(Math.random() * 5) + 1,
          sleeping: 0,
        },
        network: {
          interfaces: 2,
          connections: Math.floor(Math.random() * 20) + 5,
          bytes_rx: Math.floor(Math.random() * 1000000),
          bytes_tx: Math.floor(Math.random() * 1000000),
        },
        uptime: Math.floor(Date.now() / 1000) - 3600, // 1 hour uptime
      };

      mockStats.memory.free = mockStats.memory.total - mockStats.memory.used;
      mockStats.memory.usage_percent = (mockStats.memory.used / mockStats.memory.total) * 100;
      mockStats.processes.sleeping = mockStats.processes.total - mockStats.processes.running;

      setStats(mockStats);

      // Update history
      setCpuHistory(prev => {
        const newHistory = [...prev, { time: timeStr, cpu: mockStats.cpu.usage_percent }];
        return newHistory.slice(-20); // Keep last 20 points
      });

      setMemoryHistory(prev => {
        const newHistory = [...prev, { time: timeStr, memory: mockStats.memory.usage_percent }];
        return newHistory.slice(-20); // Keep last 20 points
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (!stats) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3, color: '#00ff41' }}>
          Loading System Dashboard...
        </Typography>
        <LinearProgress sx={{ backgroundColor: '#333', '& .MuiLinearProgress-bar': { backgroundColor: '#00ff41' } }} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, color: '#00ff41' }}>
        System Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* System Overview Cards */}
        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CpuIcon sx={{ color: '#00ff41', mr: 1 }} />
                <Typography variant="h6">CPU Usage</Typography>
              </Box>
              <Typography variant="h3" sx={{ color: '#00ff41', mb: 1 }}>
                {stats.cpu.usage_percent}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={stats.cpu.usage_percent} 
                sx={{ 
                  backgroundColor: '#333', 
                  '& .MuiLinearProgress-bar': { backgroundColor: '#00ff41' } 
                }}
              />
              <Typography variant="body2" sx={{ mt: 1, color: '#ccc' }}>
                {stats.cpu.cores} cores • Load: {stats.cpu.load_avg[0].toFixed(2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <MemoryIcon sx={{ color: '#00ff41', mr: 1 }} />
                <Typography variant="h6">Memory</Typography>
              </Box>
              <Typography variant="h3" sx={{ color: '#00ff41', mb: 1 }}>
                {stats.memory.usage_percent.toFixed(1)}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={stats.memory.usage_percent} 
                sx={{ 
                  backgroundColor: '#333', 
                  '& .MuiLinearProgress-bar': { backgroundColor: '#00ff41' } 
                }}
              />
              <Typography variant="body2" sx={{ mt: 1, color: '#ccc' }}>
                {formatBytes(stats.memory.used)} / {formatBytes(stats.memory.total)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ProcessIcon sx={{ color: '#00ff41', mr: 1 }} />
                <Typography variant="h6">Processes</Typography>
              </Box>
              <Typography variant="h3" sx={{ color: '#00ff41', mb: 1 }}>
                {stats.processes.total}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                <Chip 
                  label={`${stats.processes.running} running`} 
                  size="small" 
                  sx={{ backgroundColor: '#00ff41', color: '#000' }}
                />
                <Chip 
                  label={`${stats.processes.sleeping} sleeping`} 
                  size="small" 
                  sx={{ backgroundColor: '#333', color: '#fff' }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NetworkIcon sx={{ color: '#00ff41', mr: 1 }} />
                <Typography variant="h6">Network</Typography>
              </Box>
              <Typography variant="h3" sx={{ color: '#00ff41', mb: 1 }}>
                {stats.network.connections}
              </Typography>
              <Typography variant="body2" sx={{ color: '#ccc' }}>
                Active connections
              </Typography>
              <Typography variant="body2" sx={{ mt: 1, color: '#ccc' }}>
                ↓ {formatBytes(stats.network.bytes_rx)} ↑ {formatBytes(stats.network.bytes_tx)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Charts */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              CPU Usage History
            </Typography>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={cpuHistory}>
                <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                <XAxis dataKey="time" stroke="#ccc" />
                <YAxis stroke="#ccc" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1a1a1a', 
                    border: '1px solid #00ff41',
                    color: '#fff'
                  }} 
                />
                <Line 
                  type="monotone" 
                  dataKey="cpu" 
                  stroke="#00ff41" 
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Memory Usage History
            </Typography>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={memoryHistory}>
                <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                <XAxis dataKey="time" stroke="#ccc" />
                <YAxis stroke="#ccc" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1a1a1a', 
                    border: '1px solid #00ff41',
                    color: '#fff'
                  }} 
                />
                <Line 
                  type="monotone" 
                  dataKey="memory" 
                  stroke="#ff6b35" 
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* System Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              System Information
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <SecurityIcon sx={{ color: '#00ff41' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Kernel Version" 
                  secondary="DistributedKernel 1.0.0"
                  secondaryTypographyProps={{ color: '#ccc' }}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <StorageIcon sx={{ color: '#00ff41' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Architecture" 
                  secondary="x86_64"
                  secondaryTypographyProps={{ color: '#ccc' }}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CpuIcon sx={{ color: '#00ff41' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Uptime" 
                  secondary={formatUptime(stats.uptime)}
                  secondaryTypographyProps={{ color: '#ccc' }}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#00ff41' }}>
              Service Status
            </Typography>
            <List dense>
              <ListItem>
                <ListItemText 
                  primary="Kernel Core" 
                  secondary={<Chip label="Running" size="small" sx={{ backgroundColor: '#00ff41', color: '#000' }} />}
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Filesystem Service" 
                  secondary={<Chip label="Running" size="small" sx={{ backgroundColor: '#00ff41', color: '#000' }} />}
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Network Service" 
                  secondary={<Chip label="Running" size="small" sx={{ backgroundColor: '#00ff41', color: '#000' }} />}
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Web Bridge" 
                  secondary={<Chip label="Running" size="small" sx={{ backgroundColor: '#00ff41', color: '#000' }} />}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
