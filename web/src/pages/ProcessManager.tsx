import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Tooltip,
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Delete as KillIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

interface Process {
  id: string;
  name: string;
  state: 'running' | 'sleeping' | 'stopped' | 'zombie';
  priority: 'high' | 'normal' | 'low';
  cpu_percent: number;
  memory_mb: number;
  created_at: string;
  parent_id?: string;
}

const ProcessManager: React.FC = () => {
  const [processes, setProcesses] = useState<Process[]>([]);
  const [selectedProcess, setSelectedProcess] = useState<Process | null>(null);
  const [createDialogO<PERSON>, setCreateDialogOpen] = useState(false);
  const [newProcessName, setNewProcessName] = useState('');
  const [newProcessPriority, setNewProcessPriority] = useState<'high' | 'normal' | 'low'>('normal');

  useEffect(() => {
    // Simulate process data
    const mockProcesses: Process[] = [
      {
        id: '1',
        name: 'kernel',
        state: 'running',
        priority: 'high',
        cpu_percent: 5.2,
        memory_mb: 128,
        created_at: '2024-01-01T00:00:00Z',
      },
      {
        id: '2',
        name: 'filesystem-service',
        state: 'running',
        priority: 'normal',
        cpu_percent: 2.1,
        memory_mb: 64,
        created_at: '2024-01-01T00:01:00Z',
        parent_id: '1',
      },
      {
        id: '3',
        name: 'network-service',
        state: 'running',
        priority: 'normal',
        cpu_percent: 1.8,
        memory_mb: 48,
        created_at: '2024-01-01T00:01:30Z',
        parent_id: '1',
      },
      {
        id: '4',
        name: 'web-bridge',
        state: 'running',
        priority: 'normal',
        cpu_percent: 3.5,
        memory_mb: 96,
        created_at: '2024-01-01T00:02:00Z',
        parent_id: '1',
      },
      {
        id: '5',
        name: 'user-shell',
        state: 'sleeping',
        priority: 'low',
        cpu_percent: 0.1,
        memory_mb: 16,
        created_at: '2024-01-01T00:05:00Z',
        parent_id: '1',
      },
    ];

    setProcesses(mockProcesses);

    // Simulate real-time updates
    const interval = setInterval(() => {
      setProcesses(prev => prev.map(process => ({
        ...process,
        cpu_percent: Math.max(0, process.cpu_percent + (Math.random() - 0.5) * 2),
        memory_mb: Math.max(16, process.memory_mb + Math.floor((Math.random() - 0.5) * 10)),
      })));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const getStateColor = (state: string) => {
    switch (state) {
      case 'running': return '#00ff41';
      case 'sleeping': return '#ffff00';
      case 'stopped': return '#ff6b35';
      case 'zombie': return '#ff0000';
      default: return '#ccc';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ff6b35';
      case 'normal': return '#00ff41';
      case 'low': return '#ffff00';
      default: return '#ccc';
    }
  };

  const handleCreateProcess = () => {
    if (!newProcessName.trim()) return;

    const newProcess: Process = {
      id: (processes.length + 1).toString(),
      name: newProcessName,
      state: 'running',
      priority: newProcessPriority,
      cpu_percent: Math.random() * 5,
      memory_mb: Math.floor(Math.random() * 100) + 16,
      created_at: new Date().toISOString(),
      parent_id: '1',
    };

    setProcesses(prev => [...prev, newProcess]);
    setCreateDialogOpen(false);
    setNewProcessName('');
    setNewProcessPriority('normal');
  };

  const handleKillProcess = (processId: string) => {
    setProcesses(prev => prev.filter(p => p.id !== processId));
  };

  const handleStopProcess = (processId: string) => {
    setProcesses(prev => prev.map(p => 
      p.id === processId ? { ...p, state: 'stopped' as const } : p
    ));
  };

  const handleStartProcess = (processId: string) => {
    setProcesses(prev => prev.map(p => 
      p.id === processId ? { ...p, state: 'running' as const } : p
    ));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ color: '#00ff41' }}>
          Process Manager
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => window.location.reload()}
            sx={{ borderColor: '#00ff41', color: '#00ff41' }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            sx={{ backgroundColor: '#00ff41', color: '#000' }}
          >
            New Process
          </Button>
        </Box>
      </Box>

      <TableContainer component={Paper} sx={{ backgroundColor: '#1a1a1a', border: '1px solid #333' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>PID</TableCell>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Name</TableCell>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>State</TableCell>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Priority</TableCell>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>CPU %</TableCell>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Memory (MB)</TableCell>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Created</TableCell>
              <TableCell sx={{ color: '#00ff41', fontWeight: 'bold' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {processes.map((process) => (
              <TableRow key={process.id} hover>
                <TableCell sx={{ color: '#fff', fontFamily: 'monospace' }}>
                  {process.id}
                </TableCell>
                <TableCell sx={{ color: '#fff' }}>
                  {process.name}
                  {process.parent_id && (
                    <Typography variant="caption" sx={{ color: '#ccc', ml: 1 }}>
                      (parent: {process.parent_id})
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Chip
                    label={process.state}
                    size="small"
                    sx={{
                      backgroundColor: getStateColor(process.state),
                      color: '#000',
                      fontWeight: 'bold',
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={process.priority}
                    size="small"
                    variant="outlined"
                    sx={{
                      borderColor: getPriorityColor(process.priority),
                      color: getPriorityColor(process.priority),
                    }}
                  />
                </TableCell>
                <TableCell sx={{ color: '#fff', fontFamily: 'monospace' }}>
                  {process.cpu_percent.toFixed(1)}%
                </TableCell>
                <TableCell sx={{ color: '#fff', fontFamily: 'monospace' }}>
                  {process.memory_mb}
                </TableCell>
                <TableCell sx={{ color: '#ccc', fontSize: '0.8rem' }}>
                  {new Date(process.created_at).toLocaleString()}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <Tooltip title="Process Info">
                      <IconButton
                        size="small"
                        onClick={() => setSelectedProcess(process)}
                        sx={{ color: '#00ff41' }}
                      >
                        <InfoIcon />
                      </IconButton>
                    </Tooltip>
                    
                    {process.state === 'running' ? (
                      <Tooltip title="Stop Process">
                        <IconButton
                          size="small"
                          onClick={() => handleStopProcess(process.id)}
                          sx={{ color: '#ffff00' }}
                        >
                          <StopIcon />
                        </IconButton>
                      </Tooltip>
                    ) : (
                      <Tooltip title="Start Process">
                        <IconButton
                          size="small"
                          onClick={() => handleStartProcess(process.id)}
                          sx={{ color: '#00ff41' }}
                        >
                          <StartIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    
                    {process.name !== 'kernel' && (
                      <Tooltip title="Kill Process">
                        <IconButton
                          size="small"
                          onClick={() => handleKillProcess(process.id)}
                          sx={{ color: '#ff6b35' }}
                        >
                          <KillIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Create Process Dialog */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        PaperProps={{
          sx: { backgroundColor: '#1a1a1a', border: '1px solid #00ff41' }
        }}
      >
        <DialogTitle sx={{ color: '#00ff41' }}>Create New Process</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Process Name"
            fullWidth
            variant="outlined"
            value={newProcessName}
            onChange={(e) => setNewProcessName(e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': {
                color: '#fff',
                '& fieldset': { borderColor: '#333' },
                '&:hover fieldset': { borderColor: '#00ff41' },
                '&.Mui-focused fieldset': { borderColor: '#00ff41' },
              },
              '& .MuiInputLabel-root': { color: '#ccc' },
            }}
          />
          <TextField
            select
            margin="dense"
            label="Priority"
            fullWidth
            variant="outlined"
            value={newProcessPriority}
            onChange={(e) => setNewProcessPriority(e.target.value as 'high' | 'normal' | 'low')}
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                color: '#fff',
                '& fieldset': { borderColor: '#333' },
                '&:hover fieldset': { borderColor: '#00ff41' },
                '&.Mui-focused fieldset': { borderColor: '#00ff41' },
              },
              '& .MuiInputLabel-root': { color: '#ccc' },
            }}
          >
            <MenuItem value="high">High</MenuItem>
            <MenuItem value="normal">Normal</MenuItem>
            <MenuItem value="low">Low</MenuItem>
          </TextField>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)} sx={{ color: '#ccc' }}>
            Cancel
          </Button>
          <Button 
            onClick={handleCreateProcess} 
            variant="contained"
            sx={{ backgroundColor: '#00ff41', color: '#000' }}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Process Info Dialog */}
      <Dialog 
        open={!!selectedProcess} 
        onClose={() => setSelectedProcess(null)}
        PaperProps={{
          sx: { backgroundColor: '#1a1a1a', border: '1px solid #00ff41' }
        }}
      >
        <DialogTitle sx={{ color: '#00ff41' }}>
          Process Information - {selectedProcess?.name}
        </DialogTitle>
        <DialogContent>
          {selectedProcess && (
            <Box sx={{ color: '#fff' }}>
              <Typography><strong>PID:</strong> {selectedProcess.id}</Typography>
              <Typography><strong>State:</strong> {selectedProcess.state}</Typography>
              <Typography><strong>Priority:</strong> {selectedProcess.priority}</Typography>
              <Typography><strong>CPU Usage:</strong> {selectedProcess.cpu_percent.toFixed(1)}%</Typography>
              <Typography><strong>Memory:</strong> {selectedProcess.memory_mb} MB</Typography>
              <Typography><strong>Created:</strong> {new Date(selectedProcess.created_at).toLocaleString()}</Typography>
              {selectedProcess.parent_id && (
                <Typography><strong>Parent PID:</strong> {selectedProcess.parent_id}</Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedProcess(null)} sx={{ color: '#00ff41' }}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProcessManager;
