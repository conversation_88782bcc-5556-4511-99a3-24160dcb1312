import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Breadcrumbs,
  Link,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Chip,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  Home as HomeIcon,
  ArrowBack as BackIcon,
  Refresh as RefreshIcon,
  CreateNewFolder as NewFolderIcon,
  NoteAdd as NewFileIcon,
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

interface FileItem {
  name: string;
  path: string;
  size: number;
  is_dir: boolean;
  permissions: string;
  owner: string;
  group: string;
  modified_at: string;
}

const FileManager: React.FC = () => {
  const [currentPath, setCurrentPath] = useState('/');
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    file: FileItem;
  } | null>(null);
  
  // Dialog states
  const [newFolderDialog, setNewFolderDialog] = useState(false);
  const [newFileDialog, setNewFileDialog] = useState(false);
  const [editDialog, setEditDialog] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newFileName, setNewFileName] = useState('');
  const [fileContent, setFileContent] = useState('');

  useEffect(() => {
    loadDirectory(currentPath);
  }, [currentPath]);

  const loadDirectory = async (path: string) => {
    setLoading(true);
    try {
      // Simulate API call to get directory contents
      const mockFiles: FileItem[] = [];
      
      if (path === '/') {
        mockFiles.push(
          {
            name: 'bin',
            path: '/bin',
            size: 0,
            is_dir: true,
            permissions: 'drwxr-xr-x',
            owner: 'root',
            group: 'root',
            modified_at: new Date().toISOString(),
          },
          {
            name: 'etc',
            path: '/etc',
            size: 0,
            is_dir: true,
            permissions: 'drwxr-xr-x',
            owner: 'root',
            group: 'root',
            modified_at: new Date().toISOString(),
          },
          {
            name: 'home',
            path: '/home',
            size: 0,
            is_dir: true,
            permissions: 'drwxr-xr-x',
            owner: 'root',
            group: 'root',
            modified_at: new Date().toISOString(),
          },
          {
            name: 'tmp',
            path: '/tmp',
            size: 0,
            is_dir: true,
            permissions: 'drwxrwxrwx',
            owner: 'root',
            group: 'root',
            modified_at: new Date().toISOString(),
          }
        );
      } else if (path === '/etc') {
        mockFiles.push(
          {
            name: 'hostname',
            path: '/etc/hostname',
            size: 18,
            is_dir: false,
            permissions: '-rw-r--r--',
            owner: 'root',
            group: 'root',
            modified_at: new Date().toISOString(),
          },
          {
            name: 'version',
            path: '/etc/version',
            size: 5,
            is_dir: false,
            permissions: '-rw-r--r--',
            owner: 'root',
            group: 'root',
            modified_at: new Date().toISOString(),
          }
        );
      } else if (path === '/tmp') {
        mockFiles.push(
          {
            name: 'welcome.txt',
            path: '/tmp/welcome.txt',
            size: 35,
            is_dir: false,
            permissions: '-rw-r--r--',
            owner: 'root',
            group: 'root',
            modified_at: new Date().toISOString(),
          }
        );
      }
      
      setFiles(mockFiles);
    } catch (error) {
      console.error('Failed to load directory:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToPath = (path: string) => {
    setCurrentPath(path);
  };

  const navigateUp = () => {
    if (currentPath === '/') return;
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    setCurrentPath(parentPath);
  };

  const handleFileClick = (file: FileItem) => {
    if (file.is_dir) {
      navigateToPath(file.path);
    } else {
      setSelectedFile(file);
      loadFileContent(file.path);
      setEditDialog(true);
    }
  };

  const handleContextMenu = (event: React.MouseEvent, file: FileItem) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      file,
    });
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  const loadFileContent = async (path: string) => {
    try {
      // Simulate file content loading
      const mockContent: Record<string, string> = {
        '/etc/hostname': 'distributed-kernel',
        '/etc/version': '1.0.0',
        '/tmp/welcome.txt': 'Welcome to the Distributed Kernel!',
      };
      setFileContent(mockContent[path] || '');
    } catch (error) {
      console.error('Failed to load file content:', error);
      setFileContent('Error loading file content');
    }
  };

  const saveFileContent = async () => {
    if (!selectedFile) return;
    
    try {
      // Simulate file saving
      console.log(`Saving file ${selectedFile.path}:`, fileContent);
      setEditDialog(false);
      setSelectedFile(null);
      setFileContent('');
    } catch (error) {
      console.error('Failed to save file:', error);
    }
  };

  const createNewFolder = async () => {
    if (!newFolderName.trim()) return;
    
    try {
      // Simulate folder creation
      const newFolder: FileItem = {
        name: newFolderName,
        path: `${currentPath}/${newFolderName}`,
        size: 0,
        is_dir: true,
        permissions: 'drwxr-xr-x',
        owner: 'user',
        group: 'user',
        modified_at: new Date().toISOString(),
      };
      
      setFiles(prev => [...prev, newFolder]);
      setNewFolderDialog(false);
      setNewFolderName('');
    } catch (error) {
      console.error('Failed to create folder:', error);
    }
  };

  const createNewFile = async () => {
    if (!newFileName.trim()) return;
    
    try {
      // Simulate file creation
      const newFile: FileItem = {
        name: newFileName,
        path: `${currentPath}/${newFileName}`,
        size: 0,
        is_dir: false,
        permissions: '-rw-r--r--',
        owner: 'user',
        group: 'user',
        modified_at: new Date().toISOString(),
      };
      
      setFiles(prev => [...prev, newFile]);
      setNewFileDialog(false);
      setNewFileName('');
    } catch (error) {
      console.error('Failed to create file:', error);
    }
  };

  const deleteFile = async (file: FileItem) => {
    try {
      // Simulate file deletion
      setFiles(prev => prev.filter(f => f.path !== file.path));
      closeContextMenu();
    } catch (error) {
      console.error('Failed to delete file:', error);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getPathSegments = () => {
    if (currentPath === '/') return [{ name: 'root', path: '/' }];
    const segments = currentPath.split('/').filter(Boolean);
    return [
      { name: 'root', path: '/' },
      ...segments.map((segment, index) => ({
        name: segment,
        path: '/' + segments.slice(0, index + 1).join('/'),
      })),
    ];
  };

  return (
    <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" sx={{ color: '#00ff41' }}>
          File Manager
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={navigateUp} disabled={currentPath === '/'} sx={{ color: '#00ff41' }}>
            <BackIcon />
          </IconButton>
          <IconButton onClick={() => navigateToPath('/')} sx={{ color: '#00ff41' }}>
            <HomeIcon />
          </IconButton>
          <IconButton onClick={() => loadDirectory(currentPath)} sx={{ color: '#00ff41' }}>
            <RefreshIcon />
          </IconButton>
          <IconButton onClick={() => setNewFolderDialog(true)} sx={{ color: '#00ff41' }}>
            <NewFolderIcon />
          </IconButton>
          <IconButton onClick={() => setNewFileDialog(true)} sx={{ color: '#00ff41' }}>
            <NewFileIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 2, color: '#ccc' }}>
        {getPathSegments().map((segment, index) => (
          <Link
            key={segment.path}
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              navigateToPath(segment.path);
            }}
            sx={{ 
              color: index === getPathSegments().length - 1 ? '#00ff41' : '#ccc',
              textDecoration: 'none',
              '&:hover': { color: '#00ff41' }
            }}
          >
            {segment.name}
          </Link>
        ))}
      </Breadcrumbs>

      {/* File List */}
      <Paper sx={{ flex: 1, backgroundColor: '#1a1a1a', border: '1px solid #333', overflow: 'auto' }}>
        <List>
          {files.map((file) => (
            <ListItem
              key={file.path}
              disablePadding
              onContextMenu={(e) => handleContextMenu(e, file)}
            >
              <ListItemButton onClick={() => handleFileClick(file)}>
                <ListItemIcon>
                  {file.is_dir ? (
                    <FolderIcon sx={{ color: '#00ff41' }} />
                  ) : (
                    <FileIcon sx={{ color: '#ccc' }} />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography sx={{ color: '#fff' }}>{file.name}</Typography>
                      <Chip
                        label={file.permissions}
                        size="small"
                        sx={{ 
                          backgroundColor: '#333', 
                          color: '#ccc',
                          fontSize: '0.7rem',
                          fontFamily: 'monospace'
                        }}
                      />
                    </Box>
                  }
                  secondary={
                    <Typography variant="body2" sx={{ color: '#ccc' }}>
                      {file.is_dir ? 'Directory' : formatFileSize(file.size)} • 
                      {file.owner}:{file.group} • 
                      {new Date(file.modified_at).toLocaleDateString()}
                    </Typography>
                  }
                />
              </ListItemButton>
            </ListItem>
          ))}
          {files.length === 0 && !loading && (
            <ListItem>
              <ListItemText
                primary={
                  <Typography sx={{ color: '#ccc', textAlign: 'center', py: 4 }}>
                    Directory is empty
                  </Typography>
                }
              />
            </ListItem>
          )}
        </List>
      </Paper>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={closeContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        PaperProps={{
          sx: { backgroundColor: '#1a1a1a', border: '1px solid #333' }
        }}
      >
        <MenuItem onClick={() => {
          if (contextMenu?.file && !contextMenu.file.is_dir) {
            handleFileClick(contextMenu.file);
          }
          closeContextMenu();
        }}>
          <EditIcon sx={{ mr: 1, color: '#00ff41' }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => {
          if (contextMenu?.file) {
            deleteFile(contextMenu.file);
          }
        }}>
          <DeleteIcon sx={{ mr: 1, color: '#ff6b35' }} />
          Delete
        </MenuItem>
      </Menu>

      {/* New Folder Dialog */}
      <Dialog open={newFolderDialog} onClose={() => setNewFolderDialog(false)}>
        <DialogTitle sx={{ color: '#00ff41' }}>Create New Folder</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Folder Name"
            fullWidth
            variant="outlined"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': {
                color: '#fff',
                '& fieldset': { borderColor: '#333' },
                '&:hover fieldset': { borderColor: '#00ff41' },
                '&.Mui-focused fieldset': { borderColor: '#00ff41' },
              },
              '& .MuiInputLabel-root': { color: '#ccc' },
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewFolderDialog(false)} sx={{ color: '#ccc' }}>
            Cancel
          </Button>
          <Button onClick={createNewFolder} sx={{ color: '#00ff41' }}>
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* New File Dialog */}
      <Dialog open={newFileDialog} onClose={() => setNewFileDialog(false)}>
        <DialogTitle sx={{ color: '#00ff41' }}>Create New File</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="File Name"
            fullWidth
            variant="outlined"
            value={newFileName}
            onChange={(e) => setNewFileName(e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': {
                color: '#fff',
                '& fieldset': { borderColor: '#333' },
                '&:hover fieldset': { borderColor: '#00ff41' },
                '&.Mui-focused fieldset': { borderColor: '#00ff41' },
              },
              '& .MuiInputLabel-root': { color: '#ccc' },
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewFileDialog(false)} sx={{ color: '#ccc' }}>
            Cancel
          </Button>
          <Button onClick={createNewFile} sx={{ color: '#00ff41' }}>
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit File Dialog */}
      <Dialog 
        open={editDialog} 
        onClose={() => setEditDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ color: '#00ff41' }}>
          Edit File: {selectedFile?.name}
        </DialogTitle>
        <DialogContent>
          <TextField
            multiline
            rows={15}
            fullWidth
            variant="outlined"
            value={fileContent}
            onChange={(e) => setFileContent(e.target.value)}
            sx={{
              mt: 1,
              '& .MuiOutlinedInput-root': {
                color: '#fff',
                fontFamily: 'monospace',
                '& fieldset': { borderColor: '#333' },
                '&:hover fieldset': { borderColor: '#00ff41' },
                '&.Mui-focused fieldset': { borderColor: '#00ff41' },
              },
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)} sx={{ color: '#ccc' }}>
            Cancel
          </Button>
          <Button onClick={saveFileContent} sx={{ color: '#00ff41' }}>
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FileManager;
