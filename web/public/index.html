<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Distributed Kernel - A modern distributed operating system with web interface"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>Distributed Kernel</title>
    <style>
      body {
        margin: 0;
        background-color: #0a0a0a;
        color: #00ff41;
        font-family: 'Fira Code', 'Courier New', monospace;
      }
      .loading-screen {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background-color: #0a0a0a;
      }
      .loading-text {
        color: #00ff41;
        font-size: 24px;
        margin-bottom: 20px;
        text-shadow: 0 0 10px #00ff41;
      }
      .loading-spinner {
        border: 2px solid #333;
        border-top: 2px solid #00ff41;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <div class="loading-screen">
        <div class="loading-text">Initializing Distributed Kernel...</div>
        <div class="loading-spinner"></div>
      </div>
    </div>
  </body>
</html>
