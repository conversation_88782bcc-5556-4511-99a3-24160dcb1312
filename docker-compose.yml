version: '3.8'

services:
  distributed-kernel:
    build: .
    container_name: distributed-kernel
    ports:
      - "3000:3000"   # Web interface
      - "8080:8080"   # Bridge API
      - "50051:50051" # Filesystem service
      - "50052:50052" # Network service
    environment:
      - NODE_ENV=production
      - RUST_LOG=info
    volumes:
      - kernel-logs:/var/log/distributed-kernel
      - kernel-data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - kernel-network

  # Optional: Add monitoring with Prometheus and Grafana
  prometheus:
    image: prom/prometheus:latest
    container_name: kernel-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - kernel-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: kernel-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - kernel-network
    profiles:
      - monitoring

volumes:
  kernel-logs:
    driver: local
  kernel-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  kernel-network:
    driver: bridge
