[supervisord]
nodaemon=true
user=root
logfile=/var/log/distributed-kernel/supervisord.log
pidfile=/var/run/supervisord.pid

[program:kernel]
command=/app/bin/kernel
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/distributed-kernel/kernel.err.log
stdout_logfile=/var/log/distributed-kernel/kernel.out.log
user=root
priority=100

[program:filesystem-service]
command=/app/bin/filesystem-service
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/distributed-kernel/filesystem.err.log
stdout_logfile=/var/log/distributed-kernel/filesystem.out.log
user=root
priority=200

[program:network-service]
command=/app/bin/network-service
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/distributed-kernel/network.err.log
stdout_logfile=/var/log/distributed-kernel/network.out.log
user=root
priority=200

[program:bridge-service]
command=node /app/bridge/server.js
directory=/app/bridge
autostart=true
autorestart=true
stderr_logfile=/var/log/distributed-kernel/bridge.err.log
stdout_logfile=/var/log/distributed-kernel/bridge.out.log
user=root
priority=300
environment=NODE_ENV=production,PORT=8080

[program:web-server]
command=npx serve -s /app/web -l 3000
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/distributed-kernel/web.err.log
stdout_logfile=/var/log/distributed-kernel/web.out.log
user=root
priority=400
