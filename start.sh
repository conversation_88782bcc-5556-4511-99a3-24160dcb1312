#!/bin/bash

# Distributed Kernel Startup Script
# This script starts all components of the distributed operating system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ASCII Art Banner
echo -e "${GREEN}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    ██████╗ ██╗███████╗████████╗██████╗ ██╗██████╗ ██╗   ██╗████████╗███████╗ ║
║    ██╔══██╗██║██╔════╝╚══██╔══╝██╔══██╗██║██╔══██╗██║   ██║╚══██╔══╝██╔════╝ ║
║    ██║  ██║██║███████╗   ██║   ██████╔╝██║██████╔╝██║   ██║   ██║   █████╗   ║
║    ██║  ██║██║╚════██║   ██║   ██╔══██╗██║██╔══██╗██║   ██║   ██║   ██╔══╝   ║
║    ██████╔╝██║███████║   ██║   ██║  ██║██║██████╔╝╚██████╔╝   ██║   ███████╗ ║
║    ╚═════╝ ╚═╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝╚═════╝  ╚═════╝    ╚═╝   ╚══════╝ ║
║                                                                              ║
║                           KERNEL v1.0.0                                     ║
║                    A Modern Distributed Operating System                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

echo -e "${CYAN}Starting Distributed Kernel System...${NC}\n"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is available
port_available() {
    ! nc -z localhost $1 2>/dev/null
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}Waiting for $service_name to be ready on port $port...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port 2>/dev/null; then
            echo -e "${GREEN}✓ $service_name is ready!${NC}"
            return 0
        fi
        
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    echo -e "\n${RED}✗ $service_name failed to start within timeout${NC}"
    return 1
}

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}Shutting down Distributed Kernel...${NC}"
    
    # Kill all background processes
    if [ ! -z "$KERNEL_PID" ]; then
        echo -e "${YELLOW}Stopping kernel...${NC}"
        kill $KERNEL_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FS_PID" ]; then
        echo -e "${YELLOW}Stopping filesystem service...${NC}"
        kill $FS_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$NET_PID" ]; then
        echo -e "${YELLOW}Stopping network service...${NC}"
        kill $NET_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$BRIDGE_PID" ]; then
        echo -e "${YELLOW}Stopping bridge service...${NC}"
        kill $BRIDGE_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$WEB_PID" ]; then
        echo -e "${YELLOW}Stopping web interface...${NC}"
        kill $WEB_PID 2>/dev/null || true
    fi
    
    echo -e "${GREEN}Distributed Kernel shutdown complete.${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check prerequisites
echo -e "${BLUE}Checking prerequisites...${NC}"

if ! command_exists "cargo"; then
    echo -e "${RED}✗ Rust/Cargo not found. Please install Rust: https://rustup.rs/${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Rust/Cargo found${NC}"

if ! command_exists "go"; then
    echo -e "${RED}✗ Go not found. Please install Go: https://golang.org/dl/${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Go found${NC}"

if ! command_exists "node"; then
    echo -e "${RED}✗ Node.js not found. Please install Node.js: https://nodejs.org/${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Node.js found${NC}"

if ! command_exists "npm"; then
    echo -e "${RED}✗ npm not found. Please install npm${NC}"
    exit 1
fi
echo -e "${GREEN}✓ npm found${NC}"

# Check if ports are available
echo -e "\n${BLUE}Checking port availability...${NC}"

if ! port_available 8080; then
    echo -e "${RED}✗ Port 8080 is already in use (needed for bridge service)${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Port 8080 available${NC}"

if ! port_available 3000; then
    echo -e "${RED}✗ Port 3000 is already in use (needed for web interface)${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Port 3000 available${NC}"

if ! port_available 50051; then
    echo -e "${RED}✗ Port 50051 is already in use (needed for filesystem service)${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Port 50051 available${NC}"

if ! port_available 50052; then
    echo -e "${RED}✗ Port 50052 is already in use (needed for network service)${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Port 50052 available${NC}"

# Build components
echo -e "\n${PURPLE}Building system components...${NC}"

# Build Rust kernel
echo -e "${YELLOW}Building kernel (Rust)...${NC}"
cd kernel
if ! cargo build --release; then
    echo -e "${RED}✗ Failed to build kernel${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Kernel built successfully${NC}"
cd ..

# Build Go services
echo -e "${YELLOW}Building services (Go)...${NC}"
cd services
if ! go mod tidy; then
    echo -e "${RED}✗ Failed to download Go dependencies${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Go services prepared${NC}"
cd ..

# Build bridge service
echo -e "${YELLOW}Building bridge service (TypeScript)...${NC}"
cd bridge
if ! npm install; then
    echo -e "${RED}✗ Failed to install bridge dependencies${NC}"
    exit 1
fi
if ! npm run build; then
    echo -e "${RED}✗ Failed to build bridge service${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Bridge service built successfully${NC}"
cd ..

# Build web interface
echo -e "${YELLOW}Building web interface (React)...${NC}"
cd web
if ! npm install; then
    echo -e "${RED}✗ Failed to install web dependencies${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Web interface prepared${NC}"
cd ..

# Start services
echo -e "\n${PURPLE}Starting system services...${NC}"

# Start kernel
echo -e "${YELLOW}Starting kernel...${NC}"
cd kernel
cargo run --release > ../logs/kernel.log 2>&1 &
KERNEL_PID=$!
cd ..
echo -e "${GREEN}✓ Kernel started (PID: $KERNEL_PID)${NC}"

# Start filesystem service
echo -e "${YELLOW}Starting filesystem service...${NC}"
cd services/filesystem
go run main.go > ../../logs/filesystem.log 2>&1 &
FS_PID=$!
cd ../..
echo -e "${GREEN}✓ Filesystem service started (PID: $FS_PID)${NC}"

# Start network service
echo -e "${YELLOW}Starting network service...${NC}"
cd services/network
go run main.go > ../../logs/network.log 2>&1 &
NET_PID=$!
cd ../..
echo -e "${GREEN}✓ Network service started (PID: $NET_PID)${NC}"

# Wait for services to initialize
sleep 3

# Start bridge service
echo -e "${YELLOW}Starting bridge service...${NC}"
cd bridge
npm start > ../logs/bridge.log 2>&1 &
BRIDGE_PID=$!
cd ..
echo -e "${GREEN}✓ Bridge service started (PID: $BRIDGE_PID)${NC}"

# Wait for bridge to be ready
wait_for_service "Bridge Service" 8080

# Start web interface
echo -e "${YELLOW}Starting web interface...${NC}"
cd web
npm start > ../logs/web.log 2>&1 &
WEB_PID=$!
cd ..
echo -e "${GREEN}✓ Web interface started (PID: $WEB_PID)${NC}"

# Wait for web interface to be ready
wait_for_service "Web Interface" 3000

# System is ready
echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                                                                              ║${NC}"
echo -e "${GREEN}║                    🚀 DISTRIBUTED KERNEL IS READY! 🚀                       ║${NC}"
echo -e "${GREEN}║                                                                              ║${NC}"
echo -e "${GREEN}║  Web Interface: ${CYAN}http://localhost:3000${GREEN}                                      ║${NC}"
echo -e "${GREEN}║  Bridge API:    ${CYAN}http://localhost:8080${GREEN}                                      ║${NC}"
echo -e "${GREEN}║  Health Check:  ${CYAN}http://localhost:8080/health${GREEN}                               ║${NC}"
echo -e "${GREEN}║                                                                              ║${NC}"
echo -e "${GREEN}║  Services Running:                                                           ║${NC}"
echo -e "${GREEN}║    • Kernel Core        (PID: $KERNEL_PID)                                        ║${NC}"
echo -e "${GREEN}║    • Filesystem Service (PID: $FS_PID)                                         ║${NC}"
echo -e "${GREEN}║    • Network Service    (PID: $NET_PID)                                         ║${NC}"
echo -e "${GREEN}║    • Bridge Service     (PID: $BRIDGE_PID)                                       ║${NC}"
echo -e "${GREEN}║    • Web Interface      (PID: $WEB_PID)                                       ║${NC}"
echo -e "${GREEN}║                                                                              ║${NC}"
echo -e "${GREEN}║  Press Ctrl+C to shutdown the system                                        ║${NC}"
echo -e "${GREEN}║                                                                              ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"

# Create logs directory if it doesn't exist
mkdir -p logs

# Open web browser (optional)
if command_exists "open"; then
    echo -e "\n${CYAN}Opening web interface in browser...${NC}"
    sleep 2
    open http://localhost:3000
elif command_exists "xdg-open"; then
    echo -e "\n${CYAN}Opening web interface in browser...${NC}"
    sleep 2
    xdg-open http://localhost:3000
fi

# Keep the script running and monitor services
echo -e "\n${BLUE}Monitoring services... (Press Ctrl+C to stop)${NC}"

while true; do
    # Check if all services are still running
    if ! kill -0 $KERNEL_PID 2>/dev/null; then
        echo -e "${RED}✗ Kernel process died${NC}"
        cleanup
    fi
    
    if ! kill -0 $FS_PID 2>/dev/null; then
        echo -e "${RED}✗ Filesystem service died${NC}"
        cleanup
    fi
    
    if ! kill -0 $NET_PID 2>/dev/null; then
        echo -e "${RED}✗ Network service died${NC}"
        cleanup
    fi
    
    if ! kill -0 $BRIDGE_PID 2>/dev/null; then
        echo -e "${RED}✗ Bridge service died${NC}"
        cleanup
    fi
    
    if ! kill -0 $WEB_PID 2>/dev/null; then
        echo -e "${RED}✗ Web interface died${NC}"
        cleanup
    fi
    
    sleep 5
done
