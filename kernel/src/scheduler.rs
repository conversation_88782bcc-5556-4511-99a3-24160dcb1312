use std::collections::{VecDeque, HashMap};
use std::time::{SystemTime, Duration};
use anyhow::Result;
use tracing::{debug, info, warn};
use uuid::Uuid;

use crate::process::{Process, ProcessState, ProcessPriority, ProcessManager};

/// Time slice for round-robin scheduling (in milliseconds)
const TIME_SLICE_MS: u64 = 10;

/// Scheduler statistics
#[derive(Debug, Clone)]
pub struct SchedulerStats {
    pub total_processes: usize,
    pub running_processes: usize,
    pub ready_processes: usize,
    pub blocked_processes: usize,
    pub context_switches: u64,
    pub total_cpu_time: Duration,
}

/// Multi-level feedback queue scheduler
pub struct Scheduler {
    /// Process manager
    process_manager: ProcessManager,
    /// High priority ready queue
    high_priority_queue: VecDeque<Uuid>,
    /// Normal priority ready queue
    normal_priority_queue: VecDeque<Uuid>,
    /// Low priority ready queue
    low_priority_queue: VecDeque<Uuid>,
    /// Currently running process
    current_process: Option<Uuid>,
    /// Time when current process started running
    current_process_start_time: Option<SystemTime>,
    /// Context switch counter
    context_switches: u64,
    /// Scheduler running state
    running: bool,
}

impl Scheduler {
    /// Create a new scheduler
    pub fn new() -> Self {
        Scheduler {
            process_manager: ProcessManager::new(),
            high_priority_queue: VecDeque::new(),
            normal_priority_queue: VecDeque::new(),
            low_priority_queue: VecDeque::new(),
            current_process: None,
            current_process_start_time: None,
            context_switches: 0,
            running: false,
        }
    }
    
    /// Start the scheduler
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting scheduler");
        self.running = true;
        Ok(())
    }
    
    /// Stop the scheduler
    pub async fn stop(&mut self) -> Result<()> {
        info!("Stopping scheduler");
        self.running = false;
        
        // Terminate all running processes
        if let Some(current_id) = self.current_process {
            if let Some(process) = self.process_manager.get_process_mut(current_id) {
                process.terminate(0);
            }
        }
        
        Ok(())
    }
    
    /// Create a new process
    pub async fn create_process(&mut self, name: String, parent_id: Option<Uuid>) -> Result<Uuid> {
        let process_id = self.process_manager.create_process(name.clone(), parent_id)?;
        
        // Add to appropriate ready queue based on priority
        if let Some(process) = self.process_manager.get_process_mut(process_id) {
            process.set_state(ProcessState::Ready);
            self.add_to_ready_queue(process_id, process.priority.clone());
        }
        
        info!("Created process '{}' with ID: {}", name, process_id);
        Ok(process_id)
    }
    
    /// Add process to appropriate ready queue
    fn add_to_ready_queue(&mut self, process_id: Uuid, priority: ProcessPriority) {
        match priority {
            ProcessPriority::High => self.high_priority_queue.push_back(process_id),
            ProcessPriority::Normal => self.normal_priority_queue.push_back(process_id),
            ProcessPriority::Low => self.low_priority_queue.push_back(process_id),
        }
    }
    
    /// Get next process to run from ready queues
    fn get_next_process(&mut self) -> Option<Uuid> {
        // Check high priority queue first
        if let Some(process_id) = self.high_priority_queue.pop_front() {
            return Some(process_id);
        }
        
        // Then normal priority queue
        if let Some(process_id) = self.normal_priority_queue.pop_front() {
            return Some(process_id);
        }
        
        // Finally low priority queue
        self.low_priority_queue.pop_front()
    }
    
    /// Scheduler tick - main scheduling logic
    pub async fn tick(&mut self) -> Result<()> {
        if !self.running {
            return Ok(());
        }
        
        // Check if current process needs to be preempted
        let should_preempt = if let Some(current_id) = self.current_process {
            if let Some(process) = self.process_manager.get_process(current_id) {
                // Check if process is still alive
                if !process.is_alive() {
                    true
                } else if let Some(start_time) = self.current_process_start_time {
                    // Check time slice
                    if let Ok(elapsed) = SystemTime::now().duration_since(start_time) {
                        elapsed.as_millis() >= TIME_SLICE_MS as u128
                    } else {
                        false
                    }
                } else {
                    false
                }
            } else {
                true // Process not found
            }
        } else {
            true // No current process
        };
        
        if should_preempt {
            self.preempt_current_process().await?;
        }
        
        // If no current process, schedule next one
        if self.current_process.is_none() {
            self.schedule_next_process().await?;
        }
        
        Ok(())
    }
    
    /// Preempt the current process
    async fn preempt_current_process(&mut self) -> Result<()> {
        if let Some(current_id) = self.current_process.take() {
            if let Some(process) = self.process_manager.get_process_mut(current_id) {
                // Add CPU time used
                if let Some(start_time) = self.current_process_start_time {
                    if let Ok(elapsed) = SystemTime::now().duration_since(start_time) {
                        process.add_cpu_time(elapsed);
                    }
                }
                
                // If process is still alive, put it back in ready queue
                if process.is_alive() && process.state == ProcessState::Running {
                    process.set_state(ProcessState::Ready);
                    self.add_to_ready_queue(current_id, process.priority.clone());
                    debug!("Preempted process: {}", process.name);
                } else if process.state == ProcessState::Terminated {
                    debug!("Process terminated: {}", process.name);
                }
            }
            
            self.current_process_start_time = None;
        }
        
        Ok(())
    }
    
    /// Schedule the next process
    async fn schedule_next_process(&mut self) -> Result<()> {
        if let Some(next_id) = self.get_next_process() {
            if let Some(process) = self.process_manager.get_process_mut(next_id) {
                process.set_state(ProcessState::Running);
                self.current_process = Some(next_id);
                self.current_process_start_time = Some(SystemTime::now());
                self.context_switches += 1;
                
                debug!("Scheduled process: {} (ID: {})", process.name, next_id);
            }
        }
        
        Ok(())
    }
    
    /// Block a process (e.g., waiting for I/O)
    pub async fn block_process(&mut self, process_id: Uuid) -> Result<()> {
        if let Some(process) = self.process_manager.get_process_mut(process_id) {
            process.set_state(ProcessState::Blocked);
            
            // If this is the current process, preempt it
            if self.current_process == Some(process_id) {
                self.preempt_current_process().await?;
            }
            
            debug!("Blocked process: {}", process.name);
        }
        
        Ok(())
    }
    
    /// Unblock a process
    pub async fn unblock_process(&mut self, process_id: Uuid) -> Result<()> {
        if let Some(process) = self.process_manager.get_process_mut(process_id) {
            if process.state == ProcessState::Blocked {
                process.set_state(ProcessState::Ready);
                self.add_to_ready_queue(process_id, process.priority.clone());
                debug!("Unblocked process: {}", process.name);
            }
        }
        
        Ok(())
    }
    
    /// Terminate a process
    pub async fn terminate_process(&mut self, process_id: Uuid, exit_code: i32) -> Result<()> {
        if let Some(process) = self.process_manager.get_process_mut(process_id) {
            process.terminate(exit_code);
            
            // If this is the current process, preempt it
            if self.current_process == Some(process_id) {
                self.preempt_current_process().await?;
            }
            
            info!("Terminated process: {} (exit code: {})", process.name, exit_code);
        }
        
        Ok(())
    }
    
    /// Change process priority
    pub async fn set_process_priority(&mut self, process_id: Uuid, priority: ProcessPriority) -> Result<()> {
        if let Some(process) = self.process_manager.get_process_mut(process_id) {
            process.set_priority(priority);
            debug!("Changed priority for process: {}", process.name);
        }
        
        Ok(())
    }
    
    /// Get scheduler statistics
    pub async fn get_stats(&self) -> SchedulerStats {
        let total_processes = self.process_manager.total_count();
        let running_processes = self.process_manager.count_by_state(ProcessState::Running);
        let ready_processes = self.process_manager.count_by_state(ProcessState::Ready);
        let blocked_processes = self.process_manager.count_by_state(ProcessState::Blocked);
        
        // Calculate total CPU time
        let total_cpu_time = self.process_manager
            .list_processes()
            .iter()
            .map(|p| p.cpu_time_used)
            .fold(Duration::new(0, 0), |acc, time| acc + time);
        
        SchedulerStats {
            total_processes,
            running_processes,
            ready_processes,
            blocked_processes,
            context_switches: self.context_switches,
            total_cpu_time,
        }
    }
    
    /// Get process information
    pub async fn get_process(&self, process_id: Uuid) -> Option<Process> {
        self.process_manager.get_process(process_id).cloned()
    }
    
    /// List all processes
    pub async fn list_processes(&self) -> Vec<Process> {
        self.process_manager.list_processes().into_iter().cloned().collect()
    }
    
    /// Clean up terminated processes
    pub async fn cleanup_terminated(&mut self) -> Vec<Process> {
        self.process_manager.cleanup_terminated()
    }
}
