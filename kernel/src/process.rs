use std::collections::HashMap;
use std::time::{SystemTime, Duration};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use anyhow::Result;

/// Process state enumeration
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ProcessState {
    Created,
    Ready,
    Running,
    Blocked,
    Terminated,
}

/// Process priority levels
#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
pub enum ProcessPriority {
    High = 0,
    Normal = 1,
    Low = 2,
}

/// Process control block (PCB)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Process {
    pub id: Uuid,
    pub parent_id: Option<Uuid>,
    pub name: String,
    pub state: ProcessState,
    pub priority: ProcessPriority,
    pub created_at: SystemTime,
    pub last_scheduled: Option<SystemTime>,
    pub cpu_time_used: Duration,
    pub memory_allocations: Vec<Uuid>,
    pub open_files: Vec<String>,
    pub environment: HashMap<String, String>,
    pub working_directory: String,
    pub exit_code: Option<i32>,
}

impl Process {
    /// Create a new process
    pub fn new(name: String, parent_id: Option<Uuid>) -> Self {
        Process {
            id: Uuid::new_v4(),
            parent_id,
            name,
            state: ProcessState::Created,
            priority: ProcessPriority::Normal,
            created_at: SystemTime::now(),
            last_scheduled: None,
            cpu_time_used: Duration::new(0, 0),
            memory_allocations: Vec::new(),
            open_files: Vec::new(),
            environment: HashMap::new(),
            working_directory: "/".to_string(),
            exit_code: None,
        }
    }
    
    /// Set process priority
    pub fn set_priority(&mut self, priority: ProcessPriority) {
        self.priority = priority;
    }
    
    /// Change process state
    pub fn set_state(&mut self, state: ProcessState) {
        self.state = state;
        if state == ProcessState::Running {
            self.last_scheduled = Some(SystemTime::now());
        }
    }
    
    /// Add CPU time used
    pub fn add_cpu_time(&mut self, duration: Duration) {
        self.cpu_time_used += duration;
    }
    
    /// Add memory allocation
    pub fn add_memory_allocation(&mut self, allocation_id: Uuid) {
        self.memory_allocations.push(allocation_id);
    }
    
    /// Remove memory allocation
    pub fn remove_memory_allocation(&mut self, allocation_id: Uuid) {
        self.memory_allocations.retain(|&id| id != allocation_id);
    }
    
    /// Open a file
    pub fn open_file(&mut self, file_path: String) {
        if !self.open_files.contains(&file_path) {
            self.open_files.push(file_path);
        }
    }
    
    /// Close a file
    pub fn close_file(&mut self, file_path: &str) {
        self.open_files.retain(|f| f != file_path);
    }
    
    /// Set environment variable
    pub fn set_env(&mut self, key: String, value: String) {
        self.environment.insert(key, value);
    }
    
    /// Get environment variable
    pub fn get_env(&self, key: &str) -> Option<&String> {
        self.environment.get(key)
    }
    
    /// Set working directory
    pub fn set_working_directory(&mut self, path: String) {
        self.working_directory = path;
    }
    
    /// Terminate process with exit code
    pub fn terminate(&mut self, exit_code: i32) {
        self.state = ProcessState::Terminated;
        self.exit_code = Some(exit_code);
    }
    
    /// Check if process is alive
    pub fn is_alive(&self) -> bool {
        self.state != ProcessState::Terminated
    }
    
    /// Get process uptime
    pub fn uptime(&self) -> Duration {
        SystemTime::now()
            .duration_since(self.created_at)
            .unwrap_or(Duration::new(0, 0))
    }
}

/// Process manager for tracking all processes
pub struct ProcessManager {
    processes: HashMap<Uuid, Process>,
    next_pid: u32,
}

impl ProcessManager {
    /// Create a new process manager
    pub fn new() -> Self {
        ProcessManager {
            processes: HashMap::new(),
            next_pid: 1,
        }
    }
    
    /// Create a new process
    pub fn create_process(&mut self, name: String, parent_id: Option<Uuid>) -> Result<Uuid> {
        let mut process = Process::new(name, parent_id);
        let process_id = process.id;
        
        // Set up default environment
        process.set_env("PATH".to_string(), "/bin:/usr/bin".to_string());
        process.set_env("HOME".to_string(), "/home/<USER>".to_string());
        process.set_env("USER".to_string(), "user".to_string());
        
        self.processes.insert(process_id, process);
        Ok(process_id)
    }
    
    /// Get a process by ID
    pub fn get_process(&self, process_id: Uuid) -> Option<&Process> {
        self.processes.get(&process_id)
    }
    
    /// Get a mutable process by ID
    pub fn get_process_mut(&mut self, process_id: Uuid) -> Option<&mut Process> {
        self.processes.get_mut(&process_id)
    }
    
    /// Remove a terminated process
    pub fn remove_process(&mut self, process_id: Uuid) -> Option<Process> {
        self.processes.remove(&process_id)
    }
    
    /// List all processes
    pub fn list_processes(&self) -> Vec<&Process> {
        self.processes.values().collect()
    }
    
    /// List processes by state
    pub fn list_processes_by_state(&self, state: ProcessState) -> Vec<&Process> {
        self.processes
            .values()
            .filter(|p| p.state == state)
            .collect()
    }
    
    /// List child processes of a parent
    pub fn list_child_processes(&self, parent_id: Uuid) -> Vec<&Process> {
        self.processes
            .values()
            .filter(|p| p.parent_id == Some(parent_id))
            .collect()
    }
    
    /// Kill a process and its children
    pub fn kill_process_tree(&mut self, process_id: Uuid) -> Result<()> {
        // First, kill all child processes
        let children: Vec<Uuid> = self.processes
            .values()
            .filter(|p| p.parent_id == Some(process_id))
            .map(|p| p.id)
            .collect();
        
        for child_id in children {
            self.kill_process_tree(child_id)?;
        }
        
        // Then kill the process itself
        if let Some(process) = self.processes.get_mut(&process_id) {
            process.terminate(-1); // SIGKILL equivalent
        }
        
        Ok(())
    }
    
    /// Clean up terminated processes
    pub fn cleanup_terminated(&mut self) -> Vec<Process> {
        let terminated_ids: Vec<Uuid> = self.processes
            .iter()
            .filter(|(_, p)| p.state == ProcessState::Terminated)
            .map(|(id, _)| *id)
            .collect();
        
        let mut cleaned = Vec::new();
        for id in terminated_ids {
            if let Some(process) = self.processes.remove(&id) {
                cleaned.push(process);
            }
        }
        
        cleaned
    }
    
    /// Get process count by state
    pub fn count_by_state(&self, state: ProcessState) -> usize {
        self.processes.values().filter(|p| p.state == state).count()
    }
    
    /// Get total process count
    pub fn total_count(&self) -> usize {
        self.processes.len()
    }
}
