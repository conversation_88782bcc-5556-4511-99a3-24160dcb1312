use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error};
use anyhow::Result;

mod memory;
mod scheduler;
mod ipc;
mod syscalls;
mod process;

use memory::MemoryManager;
use scheduler::Scheduler;
use ipc::IpcManager;
use syscalls::SyscallHandler;

/// The main kernel structure that coordinates all subsystems
pub struct Kernel {
    memory_manager: Arc<RwLock<MemoryManager>>,
    scheduler: Arc<RwLock<Scheduler>>,
    ipc_manager: Arc<RwLock<IpcManager>>,
    syscall_handler: Arc<SyscallHandler>,
}

impl Kernel {
    /// Initialize a new kernel instance
    pub async fn new() -> Result<Self> {
        info!("Initializing Distributed Kernel...");
        
        let memory_manager = Arc::new(RwLock::new(MemoryManager::new()?));
        let scheduler = Arc::new(RwLock::new(Scheduler::new()));
        let ipc_manager = Arc::new(RwLock::new(IpcManager::new()));
        
        let syscall_handler = Arc::new(SyscallHandler::new(
            memory_manager.clone(),
            scheduler.clone(),
            ipc_manager.clone(),
        ));

        Ok(Kernel {
            memory_manager,
            scheduler,
            ipc_manager,
            syscall_handler,
        })
    }

    /// Start the kernel and begin processing
    pub async fn start(&self) -> Result<()> {
        info!("Starting kernel subsystems...");
        
        // Initialize memory management
        {
            let mut mm = self.memory_manager.write().await;
            mm.initialize().await?;
        }
        
        // Start the scheduler
        {
            let mut scheduler = self.scheduler.write().await;
            scheduler.start().await?;
        }
        
        // Start IPC manager
        {
            let mut ipc = self.ipc_manager.write().await;
            ipc.start().await?;
        }
        
        // Start syscall handler
        self.syscall_handler.start().await?;
        
        info!("Kernel started successfully!");
        
        // Main kernel loop
        self.main_loop().await
    }
    
    /// Main kernel execution loop
    async fn main_loop(&self) -> Result<()> {
        info!("Entering main kernel loop...");
        
        loop {
            // Process scheduler tick
            {
                let mut scheduler = self.scheduler.write().await;
                scheduler.tick().await?;
            }
            
            // Process IPC messages
            {
                let mut ipc = self.ipc_manager.write().await;
                ipc.process_messages().await?;
            }
            
            // Memory management tasks
            {
                let mut mm = self.memory_manager.write().await;
                mm.garbage_collect().await?;
            }
            
            // Small delay to prevent busy waiting
            tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
        }
    }
    
    /// Shutdown the kernel gracefully
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down kernel...");
        
        // Stop syscall handler
        self.syscall_handler.stop().await?;
        
        // Stop IPC manager
        {
            let mut ipc = self.ipc_manager.write().await;
            ipc.stop().await?;
        }
        
        // Stop scheduler
        {
            let mut scheduler = self.scheduler.write().await;
            scheduler.stop().await?;
        }
        
        info!("Kernel shutdown complete");
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();
    
    info!("Distributed Kernel v0.1.0 starting...");
    
    // Create and start the kernel
    let kernel = Kernel::new().await?;
    
    // Handle shutdown signals
    let kernel_clone = Arc::new(kernel);
    let shutdown_kernel = kernel_clone.clone();
    
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.expect("Failed to listen for ctrl+c");
        warn!("Received shutdown signal");
        if let Err(e) = shutdown_kernel.shutdown().await {
            error!("Error during shutdown: {}", e);
        }
        std::process::exit(0);
    });
    
    // Start the kernel
    kernel_clone.start().await
}
