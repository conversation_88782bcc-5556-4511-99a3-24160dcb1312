[package]
name = "distributed-kernel"
version = "0.1.0"
edition = "2021"
authors = ["Distributed Kernel Team"]
description = "A modern distributed microkernel inspired by Linux"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
crossbeam = "0.8"
parking_lot = "0.12"
thiserror = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
async-trait = "0.1"
futures = "0.3"
tonic = "0.10"
prost = "0.12"
bytes = "1.0"
dashmap = "5.0"

[build-dependencies]
tonic-build = "0.10"

[[bin]]
name = "kernel"
path = "src/main.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
