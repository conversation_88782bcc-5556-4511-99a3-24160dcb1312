{"rustc": 14389903092037495548, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 9156206050283006199, "profile": 12615952409605300238, "path": 10480309382592118619, "deps": [[3735647485472055247, "thread_local", false, 1509456680694852703], [8973061845687057626, "smallvec", false, 6698793602774511171], [11998755268370809021, "nu_ansi_term", false, 6515105119768095555], [13909326142996790163, "tracing_log", false, 6380088415234027195], [15515836537549001135, "tracing_core", false, 13637741222928042405], [16405267689229882368, "sharded_slab", false, 2027491355643792270]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-84413df4ab60e261/dep-lib-tracing_subscriber"}}], "rustflags": [], "metadata": 12822423491602284083, "config": 2202906307356721367, "compile_kind": 0}