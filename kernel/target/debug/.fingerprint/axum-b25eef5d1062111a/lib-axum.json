{"rustc": 14389903092037495548, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 2272233468398073248, "profile": 5394829218943206528, "path": 7323308449690806806, "deps": [[504931904268503175, "http", false, 9404869949780815482], [554324495028472449, "memchr", false, 5611779585631643406], [902078471074753561, "async_trait", false, 6306417909651855740], [2543237566893615891, "bytes", false, 267094634672115108], [3930354675071354477, "percent_encoding", false, 7941095875810661851], [5204382251033773414, "tower_service", false, 15174287927695243003], [5402984160842549810, "build_script_build", false, 5810523064447759966], [7470442545028885647, "mime", false, 17173162925417158882], [10633404241517405153, "serde", false, 8176024694831709789], [10821342338875855840, "tower_layer", false, 16699498820810628209], [11070927463981346568, "axum_core", false, 10407204321587411281], [11809678037142197677, "pin_project_lite", false, 14218360889283429622], [11995922566983883800, "tower", false, 16864037859855729349], [13606258873719457095, "http_body", false, 17950474226657059866], [14051957667571541382, "bitflags", false, 3740350056911447970], [14446744633799657975, "matchit", false, 6512236936282694335], [14663280588845858595, "itoa", false, 13408291314121503873], [14796620158950075325, "hyper", false, 4292161350832365787], [15255313314640684218, "sync_wrapper", false, 15569920196642438864], [16476303074998891276, "futures_util", false, 2853325054736828015]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-b25eef5d1062111a/dep-lib-axum"}}], "rustflags": [], "metadata": 17576717817111726285, "config": 2202906307356721367, "compile_kind": 0}