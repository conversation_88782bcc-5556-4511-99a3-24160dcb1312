{"$message_type":"diagnostic","message":"unused import: `HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/scheduler.rs","byte_start":33,"byte_end":40,"line_start":1,"line_end":1,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/scheduler.rs","byte_start":22,"byte_end":23,"line_start":1,"line_end":1,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/scheduler.rs","byte_start":31,"byte_end":40,"line_start":1,"line_end":1,"column_start":32,"column_end":41,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":32,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/scheduler.rs","byte_start":40,"byte_end":41,"line_start":1,"line_end":1,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:1:34\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::{VecDeque, HashMap};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/scheduler.rs","byte_start":129,"byte_end":133,"line_start":4,"line_end":4,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/scheduler.rs","byte_start":127,"byte_end":133,"line_start":4,"line_end":4,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:4:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":194,"byte_end":198,"line_start":7,"line_end":7,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/ipc.rs","byte_start":192,"byte_end":198,"line_start":7,"line_end":7,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:7:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":119,"byte_end":125,"line_start":5,"line_end":5,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":110,"byte_end":111,"line_start":5,"line_end":5,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/syscalls.rs","byte_start":117,"byte_end":125,"line_start":5,"line_end":5,"column_start":20,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":20,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/syscalls.rs","byte_start":125,"byte_end":126,"line_start":5,"line_end":5,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:5:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{Result, anyhow};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":155,"byte_end":159,"line_start":6,"line_end":6,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":153,"byte_end":159,"line_start":6,"line_end":6,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:6:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::process::ProcessPriority`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":280,"byte_end":311,"line_start":11,"line_end":11,"column_start":5,"column_end":36,"is_primary":true,"text":[{"text":"use crate::process::ProcessPriority;","highlight_start":5,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":276,"byte_end":313,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::process::ProcessPriority;","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::process::ProcessPriority`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::process::ProcessPriority;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/scheduler.rs","byte_start":5464,"byte_end":5484,"line_start":163,"line_end":163,"column_start":36,"column_end":56,"is_primary":false,"text":[{"text":"            if let Some(process) = self.process_manager.get_process_mut(current_id) {","highlight_start":36,"highlight_end":56}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":6063,"byte_end":6067,"line_start":174,"line_end":174,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"                    self.add_to_ready_queue(current_id, process.priority.clone());","highlight_start":21,"highlight_end":25}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":6099,"byte_end":6115,"line_start":174,"line_end":174,"column_start":57,"column_end":73,"is_primary":false,"text":[{"text":"                    self.add_to_ready_queue(current_id, process.priority.clone());","highlight_start":57,"highlight_end":73}],"label":"first borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:174:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Some(process) = self.process_manager.get_process_mut(current_id) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.add_to_ready_queue(current_id, process.priority.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/scheduler.rs","byte_start":2619,"byte_end":2639,"line_start":85,"line_end":85,"column_start":32,"column_end":52,"is_primary":false,"text":[{"text":"        if let Some(process) = self.process_manager.get_process_mut(process_id) {","highlight_start":32,"highlight_end":52}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":2734,"byte_end":2738,"line_start":87,"line_end":87,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"            self.add_to_ready_queue(process_id, process.priority.clone());","highlight_start":13,"highlight_end":17}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":2770,"byte_end":2786,"line_start":87,"line_end":87,"column_start":49,"column_end":65,"is_primary":false,"text":[{"text":"            self.add_to_ready_queue(process_id, process.priority.clone());","highlight_start":49,"highlight_end":65}],"label":"first borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:87:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(process) = self.process_manager.get_process_mut(process_id) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            process.set_state(ProcessState::Ready);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            self.add_to_ready_queue(process_id, process.priority.clone());\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/scheduler.rs","byte_start":7252,"byte_end":7272,"line_start":205,"line_end":205,"column_start":32,"column_end":52,"is_primary":false,"text":[{"text":"        if let Some(process) = self.process_manager.get_process_mut(process_id) {","highlight_start":32,"highlight_end":52}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":7502,"byte_end":7506,"line_start":210,"line_end":210,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"                self.preempt_current_process().await?;","highlight_start":17,"highlight_end":21}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":7610,"byte_end":7622,"line_start":213,"line_end":213,"column_start":43,"column_end":55,"is_primary":false,"text":[{"text":"            debug!(\"Blocked process: {}\", process.name);","highlight_start":43,"highlight_end":55}],"label":"first borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":101370,"byte_end":101418,"line_start":2961,"line_end":2961,"column_start":79,"column_end":127,"is_primary":false,"text":[{"text":"        $crate::valueset!(@ { (&$next, $crate::__macro_support::Option::Some(&$crate::__macro_support::format_args!($($rest)+) as &dyn Value)), $($out),* }, $next, )","highlight_start":79,"highlight_end":127}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":101700,"byte_end":101893,"line_start":2970,"line_end":2974,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            $fields.value_set($crate::valueset!(","highlight_start":31,"highlight_end":49},{"text":"                @ { },","highlight_start":1,"highlight_end":23},{"text":"                $crate::__macro_support::Iterator::next(&mut iter).expect(\"FieldSet corrupted (this is a bug)\"),","highlight_start":1,"highlight_end":113},{"text":"                $($kvs)+","highlight_start":1,"highlight_end":25},{"text":"            ))","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":28037,"byte_end":28099,"line_start":884,"line_end":884,"column_start":16,"column_end":78,"is_primary":false,"text":[{"text":"            })($crate::valueset!(__CALLSITE.metadata().fields(), $($fields)*));","highlight_start":16,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":28791,"byte_end":28842,"line_start":904,"line_end":904,"column_start":9,"column_end":60,"is_primary":false,"text":[{"text":"        $crate::event!(target: $target, $lvl, { $($arg)+ })","highlight_start":9,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":61050,"byte_end":61166,"line_start":1823,"line_end":1827,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::event!(","highlight_start":9,"highlight_end":24},{"text":"            target: module_path!(),","highlight_start":1,"highlight_end":36},{"text":"            $crate::Level::DEBUG,","highlight_start":1,"highlight_end":34},{"text":"            $($arg)+","highlight_start":1,"highlight_end":21},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/scheduler.rs","byte_start":7580,"byte_end":7623,"line_start":213,"line_end":213,"column_start":13,"column_end":56,"is_primary":false,"text":[{"text":"            debug!(\"Blocked process: {}\", process.name);","highlight_start":13,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"debug!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":51746,"byte_end":51764,"line_start":1579,"line_end":1579,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! debug {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::__macro_support::format_args!","def_site_span":{"file_name":"/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/src/rust/library/core/src/macros/mod.rs","byte_start":35370,"byte_end":35394,"line_start":1012,"line_end":1012,"column_start":5,"column_end":29,"is_primary":false,"text":[{"text":"    macro_rules! format_args {","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:210:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(process) = self.process_manager.get_process_mut(process_id) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                self.preempt_current_process().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            debug!(\"Blocked process: {}\", process.name);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/scheduler.rs","byte_start":7805,"byte_end":7825,"line_start":221,"line_end":221,"column_start":32,"column_end":52,"is_primary":false,"text":[{"text":"        if let Some(process) = self.process_manager.get_process_mut(process_id) {","highlight_start":32,"highlight_end":52}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":7984,"byte_end":7988,"line_start":224,"line_end":224,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"                self.add_to_ready_queue(process_id, process.priority.clone());","highlight_start":17,"highlight_end":21}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":8020,"byte_end":8036,"line_start":224,"line_end":224,"column_start":53,"column_end":69,"is_primary":false,"text":[{"text":"                self.add_to_ready_queue(process_id, process.priority.clone());","highlight_start":53,"highlight_end":69}],"label":"first borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:224:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(process) = self.process_manager.get_process_mut(process_id) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m224\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                self.add_to_ready_queue(process_id, process.priority.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/scheduler.rs","byte_start":8324,"byte_end":8344,"line_start":234,"line_end":234,"column_start":32,"column_end":52,"is_primary":false,"text":[{"text":"        if let Some(process) = self.process_manager.get_process_mut(process_id) {","highlight_start":32,"highlight_end":52}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":8562,"byte_end":8566,"line_start":239,"line_end":239,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"                self.preempt_current_process().await?;","highlight_start":17,"highlight_end":21}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":8688,"byte_end":8700,"line_start":242,"line_end":242,"column_start":61,"column_end":73,"is_primary":false,"text":[{"text":"            info!(\"Terminated process: {} (exit code: {})\", process.name, exit_code);","highlight_start":61,"highlight_end":73}],"label":"first borrow later used here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":101370,"byte_end":101418,"line_start":2961,"line_end":2961,"column_start":79,"column_end":127,"is_primary":false,"text":[{"text":"        $crate::valueset!(@ { (&$next, $crate::__macro_support::Option::Some(&$crate::__macro_support::format_args!($($rest)+) as &dyn Value)), $($out),* }, $next, )","highlight_start":79,"highlight_end":127}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":101700,"byte_end":101893,"line_start":2970,"line_end":2974,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            $fields.value_set($crate::valueset!(","highlight_start":31,"highlight_end":49},{"text":"                @ { },","highlight_start":1,"highlight_end":23},{"text":"                $crate::__macro_support::Iterator::next(&mut iter).expect(\"FieldSet corrupted (this is a bug)\"),","highlight_start":1,"highlight_end":113},{"text":"                $($kvs)+","highlight_start":1,"highlight_end":25},{"text":"            ))","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":28037,"byte_end":28099,"line_start":884,"line_end":884,"column_start":16,"column_end":78,"is_primary":false,"text":[{"text":"            })($crate::valueset!(__CALLSITE.metadata().fields(), $($fields)*));","highlight_start":16,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":28791,"byte_end":28842,"line_start":904,"line_end":904,"column_start":9,"column_end":60,"is_primary":false,"text":[{"text":"        $crate::event!(target: $target, $lvl, { $($arg)+ })","highlight_start":9,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":71407,"byte_end":71522,"line_start":2110,"line_end":2114,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::event!(","highlight_start":9,"highlight_end":24},{"text":"            target: module_path!(),","highlight_start":1,"highlight_end":36},{"text":"            $crate::Level::INFO,","highlight_start":1,"highlight_end":33},{"text":"            $($arg)+","highlight_start":1,"highlight_end":21},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/scheduler.rs","byte_start":8640,"byte_end":8712,"line_start":242,"line_end":242,"column_start":13,"column_end":85,"is_primary":false,"text":[{"text":"            info!(\"Terminated process: {} (exit code: {})\", process.name, exit_code);","highlight_start":13,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"info!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":62152,"byte_end":62169,"line_start":1866,"line_end":1866,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! info {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/tracing-0.1.41/src/macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::__macro_support::format_args!","def_site_span":{"file_name":"/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/src/rust/library/core/src/macros/mod.rs","byte_start":35370,"byte_end":35394,"line_start":1012,"line_end":1012,"column_start":5,"column_end":29,"is_primary":false,"text":[{"text":"    macro_rules! format_args {","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:239:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m234\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(process) = self.process_manager.get_process_mut(process_id) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                self.preempt_current_process().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            info!(\"Terminated process: {} (exit code: {})\", process.name, exit_code);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-primitive cast: `&u64` as `&i32`","code":{"code":"E0605","explanation":"An invalid cast was attempted.\n\nErroneous code examples:\n\n```compile_fail,E0605\nlet x = 0u8;\nx as Vec<u8>; // error: non-primitive cast: `u8` as `std::vec::Vec<u8>`\n\n// Another example\n\nlet v = core::ptr::null::<u8>(); // So here, `v` is a `*const u8`.\nv as &u8; // error: non-primitive cast: `*const u8` as `&u8`\n```\n\nOnly primitive types can be cast into each other. Examples:\n\n```\nlet x = 0u8;\nx as u32; // ok!\n\nlet v = core::ptr::null::<u8>();\nv as *const i8; // ok!\n```\n\nFor more information about casts, take a look at the Type cast section in\n[The Reference Book][1].\n\n[1]: https://doc.rust-lang.org/reference/expressions/operator-expr.html#type-cast-expressions\n"},"level":"error","spans":[{"file_name":"src/syscalls.rs","byte_start":6549,"byte_end":6582,"line_start":198,"line_end":198,"column_start":25,"column_end":58,"is_primary":true,"text":[{"text":"        let exit_code = args.get(0).unwrap_or(&0) as &i32;","highlight_start":25,"highlight_end":58}],"label":"an `as` expression can only be used to convert between primitive types or to coerce to a specific trait object","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0605]\u001b[0m\u001b[0m\u001b[1m: non-primitive cast: `&u64` as `&i32`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:198:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let exit_code = args.get(0).unwrap_or(&0) as &i32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9man `as` expression can only be used to convert between primitive types or to coerce to a specific trait object\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `target_bytes`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":7623,"byte_end":7635,"line_start":225,"line_end":225,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let target_bytes = &args[0..4]; // Assuming UUID is passed as 4 u64s","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":7623,"byte_end":7635,"line_start":225,"line_end":225,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let target_bytes = &args[0..4]; // Assuming UUID is passed as 4 u64s","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_target_bytes","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `target_bytes`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:225:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let target_bytes = &args[0..4]; // Assuming UUID is passed as 4 u64s\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_target_bytes`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-primitive cast: `&u64` as `&usize`","code":{"code":"E0605","explanation":"An invalid cast was attempted.\n\nErroneous code examples:\n\n```compile_fail,E0605\nlet x = 0u8;\nx as Vec<u8>; // error: non-primitive cast: `u8` as `std::vec::Vec<u8>`\n\n// Another example\n\nlet v = core::ptr::null::<u8>(); // So here, `v` is a `*const u8`.\nv as &u8; // error: non-primitive cast: `*const u8` as `&u8`\n```\n\nOnly primitive types can be cast into each other. Examples:\n\n```\nlet x = 0u8;\nx as u32; // ok!\n\nlet v = core::ptr::null::<u8>();\nv as *const i8; // ok!\n```\n\nFor more information about casts, take a look at the Type cast section in\n[The Reference Book][1].\n\n[1]: https://doc.rust-lang.org/reference/expressions/operator-expr.html#type-cast-expressions\n"},"level":"error","spans":[{"file_name":"src/syscalls.rs","byte_start":9635,"byte_end":9673,"line_start":273,"line_end":273,"column_start":20,"column_end":58,"is_primary":true,"text":[{"text":"        let size = args.get(0).unwrap_or(&4096) as &usize;","highlight_start":20,"highlight_end":58}],"label":"an `as` expression can only be used to convert between primitive types or to coerce to a specific trait object","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0605]\u001b[0m\u001b[0m\u001b[1m: non-primitive cast: `&u64` as `&usize`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:273:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let size = args.get(0).unwrap_or(&4096) as &usize;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9man `as` expression can only be used to convert between primitive types or to coerce to a specific trait object\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-primitive cast: `&u64` as `&u32`","code":{"code":"E0605","explanation":"An invalid cast was attempted.\n\nErroneous code examples:\n\n```compile_fail,E0605\nlet x = 0u8;\nx as Vec<u8>; // error: non-primitive cast: `u8` as `std::vec::Vec<u8>`\n\n// Another example\n\nlet v = core::ptr::null::<u8>(); // So here, `v` is a `*const u8`.\nv as &u8; // error: non-primitive cast: `*const u8` as `&u8`\n```\n\nOnly primitive types can be cast into each other. Examples:\n\n```\nlet x = 0u8;\nx as u32; // ok!\n\nlet v = core::ptr::null::<u8>();\nv as *const i8; // ok!\n```\n\nFor more information about casts, take a look at the Type cast section in\n[The Reference Book][1].\n\n[1]: https://doc.rust-lang.org/reference/expressions/operator-expr.html#type-cast-expressions\n"},"level":"error","spans":[{"file_name":"src/syscalls.rs","byte_start":12787,"byte_end":12824,"line_start":344,"line_end":344,"column_start":27,"column_end":64,"is_primary":true,"text":[{"text":"        let permissions = args.get(1).unwrap_or(&0o644) as &u32;","highlight_start":27,"highlight_end":64}],"label":"an `as` expression can only be used to convert between primitive types or to coerce to a specific trait object","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0605]\u001b[0m\u001b[0m\u001b[1m: non-primitive cast: `&u64` as `&u32`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:344:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m344\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let permissions = args.get(1).unwrap_or(&0o644) as &u32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9man `as` expression can only be used to convert between primitive types or to coerce to a specific trait object\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `MemoryStats: Serialize` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9890,"byte_end":9897,"line_start":279,"line_end":279,"column_start":26,"column_end":33,"is_primary":true,"text":[{"text":"        $crate::to_value(&$other).unwrap()","highlight_start":26,"highlight_end":33}],"label":"the trait `Serialize` is not implemented for `MemoryStats`, which is required by `&MemoryStats: Serialize`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":6819,"byte_end":6849,"line_start":189,"line_end":189,"column_start":60,"column_end":90,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":60,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9625,"byte_end":9686,"line_start":271,"line_end":271,"column_start":13,"column_end":74,"is_primary":false,"text":[{"text":"            $crate::json_internal!(@object object () ($($tt)+) ($($tt)+));","highlight_start":13,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1429,"byte_end":1462,"line_start":57,"line_end":57,"column_start":9,"column_end":42,"is_primary":false,"text":[{"text":"        $crate::json_internal!($($json)+)","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/syscalls.rs","byte_start":15444,"byte_end":15581,"line_start":403,"line_end":407,"column_start":30,"column_end":11,"is_primary":false,"text":[{"text":"        let combined_stats = serde_json::json!({","highlight_start":30,"highlight_end":49},{"text":"            \"memory\": memory_stats,","highlight_start":1,"highlight_end":36},{"text":"            \"scheduler\": scheduler_stats,","highlight_start":1,"highlight_end":42},{"text":"            \"ipc\": ipc_stats","highlight_start":1,"highlight_end":29},{"text":"        });","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"serde_json::json!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1302,"byte_end":1319,"line_start":54,"line_end":54,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! json {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9873,"byte_end":9889,"line_start":279,"line_end":279,"column_start":9,"column_end":25,"is_primary":false,"text":[{"text":"        $crate::to_value(&$other).unwrap()","highlight_start":9,"highlight_end":25}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":6819,"byte_end":6849,"line_start":189,"line_end":189,"column_start":60,"column_end":90,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":60,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9625,"byte_end":9686,"line_start":271,"line_end":271,"column_start":13,"column_end":74,"is_primary":false,"text":[{"text":"            $crate::json_internal!(@object object () ($($tt)+) ($($tt)+));","highlight_start":13,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1429,"byte_end":1462,"line_start":57,"line_end":57,"column_start":9,"column_end":42,"is_primary":false,"text":[{"text":"        $crate::json_internal!($($json)+)","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/syscalls.rs","byte_start":15444,"byte_end":15581,"line_start":403,"line_end":407,"column_start":30,"column_end":11,"is_primary":false,"text":[{"text":"        let combined_stats = serde_json::json!({","highlight_start":30,"highlight_end":49},{"text":"            \"memory\": memory_stats,","highlight_start":1,"highlight_end":36},{"text":"            \"scheduler\": scheduler_stats,","highlight_start":1,"highlight_end":42},{"text":"            \"ipc\": ipc_stats","highlight_start":1,"highlight_end":29},{"text":"        });","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"serde_json::json!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1302,"byte_end":1319,"line_start":54,"line_end":54,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! json {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"for local types consider adding `#[derive(serde::Serialize)]` to your `MemoryStats` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Serialize`:\n  &'a T\n  &'a mut T\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\n  (T0, T1, T2, T3, T4)\nand 139 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `&MemoryStats` to implement `Serialize`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `to_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/value/mod.rs","byte_start":29544,"byte_end":29552,"line_start":988,"line_end":988,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"pub fn to_value<T>(value: T) -> Result<Value, Error>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/value/mod.rs","byte_start":29603,"byte_end":29612,"line_start":990,"line_end":990,"column_start":8,"column_end":17,"is_primary":true,"text":[{"text":"    T: Serialize,","highlight_start":8,"highlight_end":17}],"label":"required by this bound in `to_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `MemoryStats: Serialize` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:403:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m403\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let combined_stats = serde_json::json!({\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m404\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"memory\": memory_stats,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m405\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"scheduler\": scheduler_stats,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m406\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"ipc\": ipc_stats\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m407\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        });\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|__________\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Serialize` is not implemented for `MemoryStats`, which is required by `&MemoryStats: Serialize`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Serialize)]` to your `MemoryStats` type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Serialize`:\u001b[0m\n\u001b[0m              &'a T\u001b[0m\n\u001b[0m              &'a mut T\u001b[0m\n\u001b[0m              ()\u001b[0m\n\u001b[0m              (T,)\u001b[0m\n\u001b[0m              (T0, T1)\u001b[0m\n\u001b[0m              (T0, T1, T2)\u001b[0m\n\u001b[0m              (T0, T1, T2, T3)\u001b[0m\n\u001b[0m              (T0, T1, T2, T3, T4)\u001b[0m\n\u001b[0m            and 139 others\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `&MemoryStats` to implement `Serialize`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `to_value`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/value/mod.rs:990:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m988\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn to_value<T>(value: T) -> Result<Value, Error>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m989\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m990\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    T: Serialize,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `to_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::json_internal` which comes from the expansion of the macro `serde_json::json` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `SchedulerStats: Serialize` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9890,"byte_end":9897,"line_start":279,"line_end":279,"column_start":26,"column_end":33,"is_primary":true,"text":[{"text":"        $crate::to_value(&$other).unwrap()","highlight_start":26,"highlight_end":33}],"label":"the trait `Serialize` is not implemented for `SchedulerStats`, which is required by `&SchedulerStats: Serialize`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":6819,"byte_end":6849,"line_start":189,"line_end":189,"column_start":60,"column_end":90,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":60,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":5049,"byte_end":5115,"line_start":149,"line_end":149,"column_start":9,"column_end":75,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object () ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":6768,"byte_end":6863,"line_start":189,"line_end":189,"column_start":9,"column_end":104,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":9,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9625,"byte_end":9686,"line_start":271,"line_end":271,"column_start":13,"column_end":74,"is_primary":false,"text":[{"text":"            $crate::json_internal!(@object object () ($($tt)+) ($($tt)+));","highlight_start":13,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1429,"byte_end":1462,"line_start":57,"line_end":57,"column_start":9,"column_end":42,"is_primary":false,"text":[{"text":"        $crate::json_internal!($($json)+)","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/syscalls.rs","byte_start":15444,"byte_end":15581,"line_start":403,"line_end":407,"column_start":30,"column_end":11,"is_primary":false,"text":[{"text":"        let combined_stats = serde_json::json!({","highlight_start":30,"highlight_end":49},{"text":"            \"memory\": memory_stats,","highlight_start":1,"highlight_end":36},{"text":"            \"scheduler\": scheduler_stats,","highlight_start":1,"highlight_end":42},{"text":"            \"ipc\": ipc_stats","highlight_start":1,"highlight_end":29},{"text":"        });","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"serde_json::json!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1302,"byte_end":1319,"line_start":54,"line_end":54,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! json {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9873,"byte_end":9889,"line_start":279,"line_end":279,"column_start":9,"column_end":25,"is_primary":false,"text":[{"text":"        $crate::to_value(&$other).unwrap()","highlight_start":9,"highlight_end":25}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":6819,"byte_end":6849,"line_start":189,"line_end":189,"column_start":60,"column_end":90,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":60,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":5049,"byte_end":5115,"line_start":149,"line_end":149,"column_start":9,"column_end":75,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object () ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":6768,"byte_end":6863,"line_start":189,"line_end":189,"column_start":9,"column_end":104,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":9,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":9625,"byte_end":9686,"line_start":271,"line_end":271,"column_start":13,"column_end":74,"is_primary":false,"text":[{"text":"            $crate::json_internal!(@object object () ($($tt)+) ($($tt)+));","highlight_start":13,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1429,"byte_end":1462,"line_start":57,"line_end":57,"column_start":9,"column_end":42,"is_primary":false,"text":[{"text":"        $crate::json_internal!($($json)+)","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/syscalls.rs","byte_start":15444,"byte_end":15581,"line_start":403,"line_end":407,"column_start":30,"column_end":11,"is_primary":false,"text":[{"text":"        let combined_stats = serde_json::json!({","highlight_start":30,"highlight_end":49},{"text":"            \"memory\": memory_stats,","highlight_start":1,"highlight_end":36},{"text":"            \"scheduler\": scheduler_stats,","highlight_start":1,"highlight_end":42},{"text":"            \"ipc\": ipc_stats","highlight_start":1,"highlight_end":29},{"text":"        });","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"serde_json::json!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1302,"byte_end":1319,"line_start":54,"line_end":54,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! json {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"for local types consider adding `#[derive(serde::Serialize)]` to your `SchedulerStats` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Serialize`:\n  &'a T\n  &'a mut T\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\n  (T0, T1, T2, T3, T4)\nand 139 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `&SchedulerStats` to implement `Serialize`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `to_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/value/mod.rs","byte_start":29544,"byte_end":29552,"line_start":988,"line_end":988,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"pub fn to_value<T>(value: T) -> Result<Value, Error>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/value/mod.rs","byte_start":29603,"byte_end":29612,"line_start":990,"line_end":990,"column_start":8,"column_end":17,"is_primary":true,"text":[{"text":"    T: Serialize,","highlight_start":8,"highlight_end":17}],"label":"required by this bound in `to_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `SchedulerStats: Serialize` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:403:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m403\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let combined_stats = serde_json::json!({\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m404\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"memory\": memory_stats,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m405\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"scheduler\": scheduler_stats,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m406\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"ipc\": ipc_stats\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m407\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        });\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|__________\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Serialize` is not implemented for `SchedulerStats`, which is required by `&SchedulerStats: Serialize`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Serialize)]` to your `SchedulerStats` type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Serialize`:\u001b[0m\n\u001b[0m              &'a T\u001b[0m\n\u001b[0m              &'a mut T\u001b[0m\n\u001b[0m              ()\u001b[0m\n\u001b[0m              (T,)\u001b[0m\n\u001b[0m              (T0, T1)\u001b[0m\n\u001b[0m              (T0, T1, T2)\u001b[0m\n\u001b[0m              (T0, T1, T2, T3)\u001b[0m\n\u001b[0m              (T0, T1, T2, T3, T4)\u001b[0m\n\u001b[0m            and 139 others\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `&SchedulerStats` to implement `Serialize`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `to_value`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_json-1.0.140/src/value/mod.rs:990:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m988\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn to_value<T>(value: T) -> Result<Value, Error>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m989\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m990\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    T: Serialize,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `to_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::json_internal` which comes from the expansion of the macro `serde_json::json` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Serialize` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":407,"byte_end":416,"line_start":19,"line_end":19,"column_start":24,"column_end":33,"is_primary":true,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":24,"highlight_end":33}],"label":"the trait `Serialize` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/ipc.rs","byte_start":407,"byte_end":416,"line_start":19,"line_end":19,"column_start":24,"column_end":33,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":24,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Serialize)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_derive-1.0.219/src/lib.rs","byte_start":2584,"byte_end":2642,"line_start":92,"line_end":92,"column_start":1,"column_end":59,"is_primary":false,"text":[{"text":"pub fn derive_serialize(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src/ipc.rs","byte_start":460,"byte_end":463,"line_start":21,"line_end":21,"column_start":5,"column_end":8,"is_primary":false,"text":[{"text":"    pub id: Uuid,","highlight_start":5,"highlight_end":8}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Serialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Serialize`:\n  &'a T\n  &'a mut T\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\n  (T0, T1, T2, T3, T4)\nand 139 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ipc::_::_serde::ser::SerializeStruct::serialize_field`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/ser/mod.rs","byte_start":60780,"byte_end":60795,"line_start":1864,"line_end":1864,"column_start":8,"column_end":23,"is_primary":false,"text":[{"text":"    fn serialize_field<T>(&mut self, key: &'static str, value: &T) -> Result<(), Self::Error>","highlight_start":8,"highlight_end":23}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/ser/mod.rs","byte_start":60897,"byte_end":60906,"line_start":1866,"line_end":1866,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"        T: ?Sized + Serialize;","highlight_start":21,"highlight_end":30}],"label":"required by this bound in `SerializeStruct::serialize_field`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Serialize` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:19:24\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone, Serialize, Deserialize)]\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Serialize` is not implemented for `Uuid`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct IpcMessage {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Serialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Serialize`:\u001b[0m\n\u001b[0m               &'a T\u001b[0m\n\u001b[0m               &'a mut T\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3, T4)\u001b[0m\n\u001b[0m             and 139 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ipc::_::_serde::ser::SerializeStruct::serialize_field`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/ser/mod.rs:1866:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1864\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn serialize_field<T>(&mut self, key: &'static str, value: &T) -> Result<(), Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1865\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1866\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: ?Sized + Serialize;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SerializeStruct::serialize_field`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":468,"byte_end":472,"line_start":21,"line_end":21,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"    pub id: Uuid,","highlight_start":13,"highlight_end":17}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 152 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_element`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62482,"byte_end":62494,"line_start":1730,"line_end":1730,"column_start":8,"column_end":20,"is_primary":false,"text":[{"text":"    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>","highlight_start":8,"highlight_end":20}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62564,"byte_end":62580,"line_start":1732,"line_end":1732,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        T: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `SeqAccess::next_element`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:21:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 152 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_element`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1732:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1731\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1732\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SeqAccess::next_element`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":490,"byte_end":494,"line_start":22,"line_end":22,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"    pub sender: Uuid,","highlight_start":17,"highlight_end":21}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 152 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_element`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62482,"byte_end":62494,"line_start":1730,"line_end":1730,"column_start":8,"column_end":20,"is_primary":false,"text":[{"text":"    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>","highlight_start":8,"highlight_end":20}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62564,"byte_end":62580,"line_start":1732,"line_end":1732,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        T: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `SeqAccess::next_element`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:22:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub sender: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 152 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_element`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1732:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1731\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1732\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SeqAccess::next_element`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":514,"byte_end":518,"line_start":23,"line_end":23,"column_start":19,"column_end":23,"is_primary":true,"text":[{"text":"    pub receiver: Uuid,","highlight_start":19,"highlight_end":23}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 152 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_element`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62482,"byte_end":62494,"line_start":1730,"line_end":1730,"column_start":8,"column_end":20,"is_primary":false,"text":[{"text":"    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>","highlight_start":8,"highlight_end":20}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62564,"byte_end":62580,"line_start":1732,"line_end":1732,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        T: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `SeqAccess::next_element`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:23:19\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub receiver: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 152 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_element`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1732:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1731\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1732\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SeqAccess::next_element`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":468,"byte_end":472,"line_start":21,"line_end":21,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"    pub id: Uuid,","highlight_start":13,"highlight_end":17}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 152 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66891,"byte_end":66901,"line_start":1869,"line_end":1869,"column_start":8,"column_end":18,"is_primary":false,"text":[{"text":"    fn next_value<V>(&mut self) -> Result<V, Self::Error>","highlight_start":8,"highlight_end":18}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66963,"byte_end":66979,"line_start":1871,"line_end":1871,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        V: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `MapAccess::next_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:21:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 152 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1871:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1869\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_value<V>(&mut self) -> Result<V, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1870\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1871\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        V: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `MapAccess::next_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":490,"byte_end":494,"line_start":22,"line_end":22,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"    pub sender: Uuid,","highlight_start":17,"highlight_end":21}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 152 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66891,"byte_end":66901,"line_start":1869,"line_end":1869,"column_start":8,"column_end":18,"is_primary":false,"text":[{"text":"    fn next_value<V>(&mut self) -> Result<V, Self::Error>","highlight_start":8,"highlight_end":18}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66963,"byte_end":66979,"line_start":1871,"line_end":1871,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        V: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `MapAccess::next_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:22:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub sender: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 152 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1871:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1869\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_value<V>(&mut self) -> Result<V, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1870\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1871\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        V: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `MapAccess::next_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":514,"byte_end":518,"line_start":23,"line_end":23,"column_start":19,"column_end":23,"is_primary":true,"text":[{"text":"    pub receiver: Uuid,","highlight_start":19,"highlight_end":23}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 152 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66891,"byte_end":66901,"line_start":1869,"line_end":1869,"column_start":8,"column_end":18,"is_primary":false,"text":[{"text":"    fn next_value<V>(&mut self) -> Result<V, Self::Error>","highlight_start":8,"highlight_end":18}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66963,"byte_end":66979,"line_start":1871,"line_end":1871,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        V: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `MapAccess::next_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:23:19\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub receiver: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 152 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1871:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1869\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_value<V>(&mut self) -> Result<V, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1870\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1871\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        V: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `MapAccess::next_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ipc.rs","byte_start":418,"byte_end":429,"line_start":19,"line_end":19,"column_start":35,"column_end":46,"is_primary":true,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":35,"highlight_end":46}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/ipc.rs","byte_start":418,"byte_end":429,"line_start":19,"line_end":19,"column_start":35,"column_end":46,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":35,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Deserialize)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_derive-1.0.219/src/lib.rs","byte_start":2880,"byte_end":2940,"line_start":100,"line_end":100,"column_start":1,"column_end":61,"is_primary":false,"text":[{"text":"pub fn derive_deserialize(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 152 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ipc::_::_serde::__private::de::missing_field`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/private/de.rs","byte_start":787,"byte_end":800,"line_start":23,"line_end":23,"column_start":8,"column_end":21,"is_primary":false,"text":[{"text":"pub fn missing_field<'de, V, E>(field: &'static str) -> Result<V, E>","highlight_start":8,"highlight_end":21}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/private/de.rs","byte_start":862,"byte_end":878,"line_start":25,"line_end":25,"column_start":8,"column_end":24,"is_primary":true,"text":[{"text":"    V: Deserialize<'de>,","highlight_start":8,"highlight_end":24}],"label":"required by this bound in `missing_field`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:19:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone, Serialize, Deserialize)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m             &'a Path\u001b[0m\n\u001b[0m             &'a [u8]\u001b[0m\n\u001b[0m             &'a str\u001b[0m\n\u001b[0m             ()\u001b[0m\n\u001b[0m             (T,)\u001b[0m\n\u001b[0m             (T0, T1)\u001b[0m\n\u001b[0m             (T0, T1, T2)\u001b[0m\n\u001b[0m             (T0, T1, T2, T3)\u001b[0m\n\u001b[0m           and 152 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ipc::_::_serde::__private::de::missing_field`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/private/de.rs:25:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn missing_field<'de, V, E>(field: &'static str) -> Result<V, E>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    V: Deserialize<'de>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `missing_field`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Serialize` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":541,"byte_end":550,"line_start":26,"line_end":26,"column_start":24,"column_end":33,"is_primary":true,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":24,"highlight_end":33}],"label":"the trait `Serialize` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/process.rs","byte_start":541,"byte_end":550,"line_start":26,"line_end":26,"column_start":24,"column_end":33,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":24,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Serialize)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_derive-1.0.219/src/lib.rs","byte_start":2584,"byte_end":2642,"line_start":92,"line_end":92,"column_start":1,"column_end":59,"is_primary":false,"text":[{"text":"pub fn derive_serialize(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src/process.rs","byte_start":591,"byte_end":594,"line_start":28,"line_end":28,"column_start":5,"column_end":8,"is_primary":false,"text":[{"text":"    pub id: Uuid,","highlight_start":5,"highlight_end":8}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Serialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Serialize`:\n  &'a T\n  &'a mut T\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\n  (T0, T1, T2, T3, T4)\nand 139 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ipc::_::_serde::ser::SerializeStruct::serialize_field`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/ser/mod.rs","byte_start":60780,"byte_end":60795,"line_start":1864,"line_end":1864,"column_start":8,"column_end":23,"is_primary":false,"text":[{"text":"    fn serialize_field<T>(&mut self, key: &'static str, value: &T) -> Result<(), Self::Error>","highlight_start":8,"highlight_end":23}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/ser/mod.rs","byte_start":60897,"byte_end":60906,"line_start":1866,"line_end":1866,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"        T: ?Sized + Serialize;","highlight_start":21,"highlight_end":30}],"label":"required by this bound in `SerializeStruct::serialize_field`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Serialize` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:26:24\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone, Serialize, Deserialize)]\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Serialize` is not implemented for `Uuid`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Process {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Serialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Serialize`:\u001b[0m\n\u001b[0m               &'a T\u001b[0m\n\u001b[0m               &'a mut T\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3, T4)\u001b[0m\n\u001b[0m             and 139 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ipc::_::_serde::ser::SerializeStruct::serialize_field`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/ser/mod.rs:1866:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1864\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn serialize_field<T>(&mut self, key: &'static str, value: &T) -> Result<(), Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1865\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1866\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: ?Sized + Serialize;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SerializeStruct::serialize_field`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":599,"byte_end":603,"line_start":28,"line_end":28,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"    pub id: Uuid,","highlight_start":13,"highlight_end":17}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 153 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_element`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62482,"byte_end":62494,"line_start":1730,"line_end":1730,"column_start":8,"column_end":20,"is_primary":false,"text":[{"text":"    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>","highlight_start":8,"highlight_end":20}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62564,"byte_end":62580,"line_start":1732,"line_end":1732,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        T: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `SeqAccess::next_element`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:28:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 153 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_element`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1732:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1731\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1732\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SeqAccess::next_element`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":624,"byte_end":636,"line_start":29,"line_end":29,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"    pub parent_id: Option<Uuid>,","highlight_start":20,"highlight_end":32}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `std::option::Option<Uuid>: Deserialize<'_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 153 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `std::option::Option<Uuid>` to implement `Deserialize<'_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_element`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62482,"byte_end":62494,"line_start":1730,"line_end":1730,"column_start":8,"column_end":20,"is_primary":false,"text":[{"text":"    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>","highlight_start":8,"highlight_end":20}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62564,"byte_end":62580,"line_start":1732,"line_end":1732,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        T: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `SeqAccess::next_element`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:29:20\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub parent_id: Option<Uuid>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `std::option::Option<Uuid>: Deserialize<'_>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 153 others\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `std::option::Option<Uuid>` to implement `Deserialize<'_>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_element`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1732:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1731\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1732\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SeqAccess::next_element`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":861,"byte_end":870,"line_start":36,"line_end":36,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"    pub memory_allocations: Vec<Uuid>,","highlight_start":29,"highlight_end":38}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `Vec<Uuid>: Deserialize<'_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 153 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `Vec<Uuid>` to implement `Deserialize<'_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_element`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62482,"byte_end":62494,"line_start":1730,"line_end":1730,"column_start":8,"column_end":20,"is_primary":false,"text":[{"text":"    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>","highlight_start":8,"highlight_end":20}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":62564,"byte_end":62580,"line_start":1732,"line_end":1732,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        T: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `SeqAccess::next_element`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:36:29\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub memory_allocations: Vec<Uuid>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `Vec<Uuid>: Deserialize<'_>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 153 others\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `Vec<Uuid>` to implement `Deserialize<'_>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_element`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1732:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_element<T>(&mut self) -> Result<Option<T>, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1731\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1732\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `SeqAccess::next_element`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":599,"byte_end":603,"line_start":28,"line_end":28,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"    pub id: Uuid,","highlight_start":13,"highlight_end":17}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 153 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66891,"byte_end":66901,"line_start":1869,"line_end":1869,"column_start":8,"column_end":18,"is_primary":false,"text":[{"text":"    fn next_value<V>(&mut self) -> Result<V, Self::Error>","highlight_start":8,"highlight_end":18}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66963,"byte_end":66979,"line_start":1871,"line_end":1871,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        V: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `MapAccess::next_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:28:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 153 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1871:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1869\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_value<V>(&mut self) -> Result<V, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1870\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1871\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        V: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `MapAccess::next_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":624,"byte_end":636,"line_start":29,"line_end":29,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"    pub parent_id: Option<Uuid>,","highlight_start":20,"highlight_end":32}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `std::option::Option<Uuid>: Deserialize<'_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 153 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `std::option::Option<Uuid>` to implement `Deserialize<'_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66891,"byte_end":66901,"line_start":1869,"line_end":1869,"column_start":8,"column_end":18,"is_primary":false,"text":[{"text":"    fn next_value<V>(&mut self) -> Result<V, Self::Error>","highlight_start":8,"highlight_end":18}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66963,"byte_end":66979,"line_start":1871,"line_end":1871,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        V: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `MapAccess::next_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:29:20\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub parent_id: Option<Uuid>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `std::option::Option<Uuid>: Deserialize<'_>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 153 others\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `std::option::Option<Uuid>` to implement `Deserialize<'_>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1871:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1869\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_value<V>(&mut self) -> Result<V, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1870\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1871\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        V: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `MapAccess::next_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":861,"byte_end":870,"line_start":36,"line_end":36,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"    pub memory_allocations: Vec<Uuid>,","highlight_start":29,"highlight_end":38}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `Vec<Uuid>: Deserialize<'_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 153 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `Vec<Uuid>` to implement `Deserialize<'_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `next_value`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66891,"byte_end":66901,"line_start":1869,"line_end":1869,"column_start":8,"column_end":18,"is_primary":false,"text":[{"text":"    fn next_value<V>(&mut self) -> Result<V, Self::Error>","highlight_start":8,"highlight_end":18}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs","byte_start":66963,"byte_end":66979,"line_start":1871,"line_end":1871,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"        V: Deserialize<'de>,","highlight_start":12,"highlight_end":28}],"label":"required by this bound in `MapAccess::next_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:36:29\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub memory_allocations: Vec<Uuid>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`, which is required by `Vec<Uuid>: Deserialize<'_>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m               &'a Path\u001b[0m\n\u001b[0m               &'a [u8]\u001b[0m\n\u001b[0m               &'a str\u001b[0m\n\u001b[0m               ()\u001b[0m\n\u001b[0m               (T,)\u001b[0m\n\u001b[0m               (T0, T1)\u001b[0m\n\u001b[0m               (T0, T1, T2)\u001b[0m\n\u001b[0m               (T0, T1, T2, T3)\u001b[0m\n\u001b[0m             and 153 others\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `Vec<Uuid>` to implement `Deserialize<'_>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `next_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/de/mod.rs:1871:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1869\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn next_value<V>(&mut self) -> Result<V, Self::Error>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1870\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1871\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        V: Deserialize<'de>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `MapAccess::next_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Uuid: Deserialize<'_>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":552,"byte_end":563,"line_start":26,"line_end":26,"column_start":35,"column_end":46,"is_primary":true,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":35,"highlight_end":46}],"label":"the trait `Deserialize<'_>` is not implemented for `Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/process.rs","byte_start":552,"byte_end":563,"line_start":26,"line_end":26,"column_start":35,"column_end":46,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone, Serialize, Deserialize)]","highlight_start":35,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Deserialize)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde_derive-1.0.219/src/lib.rs","byte_start":2880,"byte_end":2940,"line_start":100,"line_end":100,"column_start":1,"column_end":61,"is_primary":false,"text":[{"text":"pub fn derive_deserialize(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `Deserialize<'de>`:\n  &'a Path\n  &'a [u8]\n  &'a str\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\nand 153 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ipc::_::_serde::__private::de::missing_field`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/private/de.rs","byte_start":787,"byte_end":800,"line_start":23,"line_end":23,"column_start":8,"column_end":21,"is_primary":false,"text":[{"text":"pub fn missing_field<'de, V, E>(field: &'static str) -> Result<V, E>","highlight_start":8,"highlight_end":21}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/private/de.rs","byte_start":862,"byte_end":878,"line_start":25,"line_end":25,"column_start":8,"column_end":24,"is_primary":true,"text":[{"text":"    V: Deserialize<'de>,","highlight_start":8,"highlight_end":24}],"label":"required by this bound in `missing_field`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Uuid: Deserialize<'_>` is not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:26:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone, Serialize, Deserialize)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Deserialize<'_>` is not implemented for `Uuid`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Deserialize)]` to your `Uuid` type\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `Deserialize<'de>`:\u001b[0m\n\u001b[0m             &'a Path\u001b[0m\n\u001b[0m             &'a [u8]\u001b[0m\n\u001b[0m             &'a str\u001b[0m\n\u001b[0m             ()\u001b[0m\n\u001b[0m             (T,)\u001b[0m\n\u001b[0m             (T0, T1)\u001b[0m\n\u001b[0m             (T0, T1, T2)\u001b[0m\n\u001b[0m             (T0, T1, T2, T3)\u001b[0m\n\u001b[0m           and 153 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ipc::_::_serde::__private::de::missing_field`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/serde-1.0.219/src/private/de.rs:25:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn missing_field<'de, V, E>(field: &'static str) -> Result<V, E>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    V: Deserialize<'de>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `missing_field`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"borrow of moved value: `state`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src/process.rs","byte_start":1896,"byte_end":1901,"line_start":70,"line_end":70,"column_start":22,"column_end":27,"is_primary":false,"text":[{"text":"        self.state = state;","highlight_start":22,"highlight_end":27}],"label":"value moved here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":1914,"byte_end":1919,"line_start":71,"line_end":71,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"        if state == ProcessState::Running {","highlight_start":12,"highlight_end":17}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":1852,"byte_end":1857,"line_start":69,"line_end":69,"column_start":33,"column_end":38,"is_primary":false,"text":[{"text":"    pub fn set_state(&mut self, state: ProcessState) {","highlight_start":33,"highlight_end":38}],"label":"move occurs because `state` has type `ProcessState`, which does not implement the `Copy` trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider cloning the value if the performance cost is acceptable","code":null,"level":"help","spans":[{"file_name":"src/process.rs","byte_start":1901,"byte_end":1901,"line_start":70,"line_end":70,"column_start":27,"column_end":27,"is_primary":true,"text":[{"text":"        self.state = state;","highlight_start":27,"highlight_end":27}],"label":null,"suggested_replacement":".clone()","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m\u001b[1m: borrow of moved value: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:71:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_state(&mut self, state: ProcessState) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmove occurs because `state` has type `ProcessState`, which does not implement the `Copy` trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.state = state;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvalue moved here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if state == ProcessState::Running {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvalue borrowed here after move\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider cloning the value if the performance cost is acceptable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        self.state = state\u001b[0m\u001b[0m\u001b[38;5;10m.clone()\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[38;5;10m++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 27 previous errors; 7 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 27 previous errors; 7 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0277, E0382, E0499, E0605.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0277, E0382, E0499, E0605.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0277`.\u001b[0m\n"}
