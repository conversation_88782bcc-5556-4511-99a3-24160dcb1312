{"$message_type":"diagnostic","message":"unused import: `HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/scheduler.rs","byte_start":33,"byte_end":40,"line_start":1,"line_end":1,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/scheduler.rs","byte_start":22,"byte_end":23,"line_start":1,"line_end":1,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/scheduler.rs","byte_start":31,"byte_end":40,"line_start":1,"line_end":1,"column_start":32,"column_end":41,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":32,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/scheduler.rs","byte_start":40,"byte_end":41,"line_start":1,"line_end":1,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use std::collections::{VecDeque, HashMap};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:1:34\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::{VecDeque, HashMap};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/scheduler.rs","byte_start":129,"byte_end":133,"line_start":4,"line_end":4,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/scheduler.rs","byte_start":127,"byte_end":133,"line_start":4,"line_end":4,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:4:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":194,"byte_end":198,"line_start":7,"line_end":7,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/ipc.rs","byte_start":192,"byte_end":198,"line_start":7,"line_end":7,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:7:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":119,"byte_end":125,"line_start":5,"line_end":5,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":110,"byte_end":111,"line_start":5,"line_end":5,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/syscalls.rs","byte_start":117,"byte_end":125,"line_start":5,"line_end":5,"column_start":20,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":20,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/syscalls.rs","byte_start":125,"byte_end":126,"line_start":5,"line_end":5,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:5:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{Result, anyhow};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":155,"byte_end":159,"line_start":6,"line_end":6,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":153,"byte_end":159,"line_start":6,"line_end":6,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:6:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::process::ProcessPriority`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":280,"byte_end":311,"line_start":11,"line_end":11,"column_start":5,"column_end":36,"is_primary":true,"text":[{"text":"use crate::process::ProcessPriority;","highlight_start":5,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":276,"byte_end":313,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::process::ProcessPriority;","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::process::ProcessPriority`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::process::ProcessPriority;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `target_bytes`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":7474,"byte_end":7486,"line_start":225,"line_end":225,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let target_bytes = &args[0..4]; // Assuming UUID is passed as 4 u64s","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/syscalls.rs","byte_start":7474,"byte_end":7486,"line_start":225,"line_end":225,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let target_bytes = &args[0..4]; // Assuming UUID is passed as 4 u64s","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_target_bytes","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `target_bytes`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:225:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let target_bytes = &args[0..4]; // Assuming UUID is passed as 4 u64s\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_target_bytes`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `id` and `virtual_addr` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/memory.rs","byte_start":292,"byte_end":308,"line_start":12,"line_end":12,"column_start":12,"column_end":28,"is_primary":false,"text":[{"text":"pub struct MemoryAllocation {","highlight_start":12,"highlight_end":28}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":319,"byte_end":321,"line_start":13,"line_end":13,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"    pub id: Uuid,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":358,"byte_end":370,"line_start":15,"line_end":15,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    pub virtual_addr: usize,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MemoryAllocation` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `id` and `virtual_addr` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/memory.rs:13:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MemoryAllocation {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub size: usize,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub virtual_addr: usize,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `MemoryAllocation` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `next_virtual_addr` and `next_physical_addr` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/memory.rs","byte_start":791,"byte_end":804,"line_start":32,"line_end":32,"column_start":12,"column_end":25,"is_primary":false,"text":[{"text":"pub struct MemoryManager {","highlight_start":12,"highlight_end":25}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":1119,"byte_end":1136,"line_start":42,"line_end":42,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    next_virtual_addr: AtomicUsize,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":1197,"byte_end":1215,"line_start":44,"line_end":44,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    next_physical_addr: AtomicUsize,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `next_virtual_addr` and `next_physical_addr` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/memory.rs:42:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MemoryManager {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    next_virtual_addr: AtomicUsize,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Next physical address to allocate\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    next_physical_addr: AtomicUsize,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `allocate`, `get_stats`, `get_allocation`, and `get_process_allocations` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/memory.rs","byte_start":1233,"byte_end":1251,"line_start":47,"line_end":47,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl MemoryManager {","highlight_start":1,"highlight_end":19}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":2308,"byte_end":2316,"line_start":79,"line_end":79,"column_start":18,"column_end":26,"is_primary":true,"text":[{"text":"    pub async fn allocate(&mut self, size: usize, process_id: Option<Uuid>) -> Result<Uuid> {","highlight_start":18,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":4847,"byte_end":4856,"line_start":148,"line_end":148,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn get_stats(&self) -> MemoryStats {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":6064,"byte_end":6078,"line_start":184,"line_end":184,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn get_allocation(&self, allocation_id: Uuid) -> Option<MemoryAllocation> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/memory.rs","byte_start":6258,"byte_end":6281,"line_start":189,"line_end":189,"column_start":18,"column_end":41,"is_primary":true,"text":[{"text":"    pub async fn get_process_allocations(&self, process_id: Uuid) -> Vec<MemoryAllocation> {","highlight_start":18,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `allocate`, `get_stats`, `get_allocation`, and `get_process_allocations` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/memory.rs:79:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MemoryManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn allocate(&mut self, size: usize, process_id: Option<Uuid>) -> Result<Uuid> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_stats(&self) -> MemoryStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m184\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_allocation(&self, allocation_id: Uuid) -> Option<MemoryAllocation> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_process_allocations(&self, process_id: Uuid) -> Vec<MemoryAllocation> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/scheduler.rs","byte_start":1254,"byte_end":1268,"line_start":43,"line_end":43,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl Scheduler {","highlight_start":1,"highlight_end":15}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":2338,"byte_end":2352,"line_start":81,"line_end":81,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn create_process(&mut self, name: String, parent_id: Option<Uuid>) -> Result<Uuid> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":7385,"byte_end":7398,"line_start":217,"line_end":217,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    pub async fn block_process(&mut self, process_id: Uuid) -> Result<()> {","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":8120,"byte_end":8135,"line_start":238,"line_end":238,"column_start":18,"column_end":33,"is_primary":true,"text":[{"text":"    pub async fn unblock_process(&mut self, process_id: Uuid) -> Result<()> {","highlight_start":18,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":9070,"byte_end":9087,"line_start":263,"line_end":263,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"    pub async fn terminate_process(&mut self, process_id: Uuid, exit_code: i32) -> Result<()> {","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":9848,"byte_end":9868,"line_start":284,"line_end":284,"column_start":18,"column_end":38,"is_primary":true,"text":[{"text":"    pub async fn set_process_priority(&mut self, process_id: Uuid, priority: ProcessPriority) -> Result<()> {","highlight_start":18,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":10220,"byte_end":10229,"line_start":294,"line_end":294,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn get_stats(&self) -> SchedulerStats {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":11127,"byte_end":11138,"line_start":318,"line_end":318,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn get_process(&self, process_id: Uuid) -> Option<Process> {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":11298,"byte_end":11312,"line_start":323,"line_end":323,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn list_processes(&self) -> Vec<Process> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/scheduler.rs","byte_start":11477,"byte_end":11495,"line_start":328,"line_end":328,"column_start":18,"column_end":36,"is_primary":true,"text":[{"text":"    pub async fn cleanup_terminated(&mut self) -> Vec<Process> {","highlight_start":18,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/scheduler.rs:81:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Scheduler {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m81\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn create_process(&mut self, name: String, parent_id: Option<Uuid>) -> Result<Uuid> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m217\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn block_process(&mut self, process_id: Uuid) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m238\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn unblock_process(&mut self, process_id: Uuid) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m263\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn terminate_process(&mut self, process_id: Uuid, exit_code: i32) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m284\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn set_process_priority(&mut self, process_id: Uuid, priority: ProcessPriority) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_stats(&self) -> SchedulerStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m318\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_process(&self, process_id: Uuid) -> Option<Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn list_processes(&self) -> Vec<Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn cleanup_terminated(&mut self) -> Vec<Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `new` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":608,"byte_end":623,"line_start":29,"line_end":29,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl IpcMessage {","highlight_start":1,"highlight_end":16}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":670,"byte_end":673,"line_start":31,"line_end":31,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated function `new` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:31:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl IpcMessage {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Create a new IPC message\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `id`, `sender`, `receiver`, `process_id`, and `created_at` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":1170,"byte_end":1180,"line_start":52,"line_end":52,"column_start":12,"column_end":22,"is_primary":false,"text":[{"text":"pub struct IpcChannel {","highlight_start":12,"highlight_end":22}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1191,"byte_end":1193,"line_start":53,"line_end":53,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"    pub id: Uuid,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1209,"byte_end":1215,"line_start":54,"line_end":54,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    pub sender: mpsc::UnboundedSender<IpcMessage>,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1260,"byte_end":1268,"line_start":55,"line_end":55,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    pub receiver: Arc<RwLock<mpsc::UnboundedReceiver<IpcMessage>>>,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1328,"byte_end":1338,"line_start":56,"line_end":56,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub process_id: Uuid,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1354,"byte_end":1364,"line_start":57,"line_end":57,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub created_at: std::time::SystemTime,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `id`, `sender`, `receiver`, `process_id`, and `created_at` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:53:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct IpcChannel {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub sender: mpsc::UnboundedSender<IpcMessage>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub receiver: Arc<RwLock<mpsc::UnboundedReceiver<IpcMessage>>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m56\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub process_id: Uuid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub created_at: std::time::SystemTime,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `new` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":1392,"byte_end":1407,"line_start":60,"line_end":60,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl IpcChannel {","highlight_start":1,"highlight_end":16}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1454,"byte_end":1457,"line_start":62,"line_end":62,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(process_id: Uuid) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated function `new` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:62:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl IpcChannel {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Create a new IPC channel\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(process_id: Uuid) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple fields are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":1841,"byte_end":1860,"line_start":77,"line_end":77,"column_start":12,"column_end":31,"is_primary":false,"text":[{"text":"pub struct SharedMemorySegment {","highlight_start":12,"highlight_end":31}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1871,"byte_end":1873,"line_start":78,"line_end":78,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"    pub id: Uuid,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1889,"byte_end":1893,"line_start":79,"line_end":79,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub name: String,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1911,"byte_end":1915,"line_start":80,"line_end":80,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub size: usize,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1932,"byte_end":1936,"line_start":81,"line_end":81,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub data: Arc<RwLock<Vec<u8>>>,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1968,"byte_end":1973,"line_start":82,"line_end":82,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    pub owner: Uuid,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":1989,"byte_end":2000,"line_start":83,"line_end":83,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"    pub permissions: u32,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":2015,"byte_end":2033,"line_start":84,"line_end":84,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    pub attached_processes: Vec<Uuid>,","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":2054,"byte_end":2064,"line_start":85,"line_end":85,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    pub created_at: std::time::SystemTime,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`SharedMemorySegment` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple fields are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:78:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct SharedMemorySegment {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub id: Uuid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub name: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub size: usize,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub data: Arc<RwLock<Vec<u8>>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub owner: Uuid,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m83\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub permissions: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub attached_processes: Vec<Uuid>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub created_at: std::time::SystemTime,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `SharedMemorySegment` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new`, `attach_process`, and `detach_process` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":2092,"byte_end":2116,"line_start":88,"line_end":88,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"impl SharedMemorySegment {","highlight_start":1,"highlight_end":25}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":2173,"byte_end":2176,"line_start":90,"line_end":90,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(name: String, size: usize, owner: Uuid, permissions: u32) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":2629,"byte_end":2643,"line_start":104,"line_end":104,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn attach_process(&mut self, process_id: Uuid) -> Result<()> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":3001,"byte_end":3015,"line_start":113,"line_end":113,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn detach_process(&mut self, process_id: Uuid) -> Result<()> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new`, `attach_process`, and `detach_process` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:90:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m88\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SharedMemorySegment {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m89\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Create a new shared memory segment\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m90\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(name: String, size: usize, owner: Uuid, permissions: u32) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn attach_process(&mut self, process_id: Uuid) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn detach_process(&mut self, process_id: Uuid) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `channels`, `shared_memory`, `named_shared_memory`, and `message_sender` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":3304,"byte_end":3314,"line_start":121,"line_end":121,"column_start":12,"column_end":22,"is_primary":false,"text":[{"text":"pub struct IpcManager {","highlight_start":12,"highlight_end":22}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":3366,"byte_end":3374,"line_start":123,"line_end":123,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    channels: HashMap<Uuid, IpcChannel>,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":3438,"byte_end":3451,"line_start":125,"line_end":125,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    shared_memory: HashMap<Uuid, SharedMemorySegment>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":3528,"byte_end":3547,"line_start":127,"line_end":127,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    named_shared_memory: HashMap<String, Uuid>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":3709,"byte_end":3723,"line_start":131,"line_end":131,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    message_sender: mpsc::UnboundedSender<IpcMessage>,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `channels`, `shared_memory`, `named_shared_memory`, and `message_sender` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:123:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m121\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct IpcManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Process channels for message passing\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    channels: HashMap<Uuid, IpcChannel>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Shared memory segments\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    shared_memory: HashMap<Uuid, SharedMemorySegment>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Named shared memory lookup\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    named_shared_memory: HashMap<String, Uuid>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    message_sender: mpsc::UnboundedSender<IpcMessage>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/ipc.rs","byte_start":3804,"byte_end":3819,"line_start":136,"line_end":136,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl IpcManager {","highlight_start":1,"highlight_end":16}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":4630,"byte_end":4644,"line_start":166,"line_end":166,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn create_channel(&mut self, process_id: Uuid) -> Result<Uuid> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":5008,"byte_end":5022,"line_start":177,"line_end":177,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn remove_channel(&mut self, process_id: Uuid) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":5290,"byte_end":5302,"line_start":185,"line_end":185,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    pub async fn send_message(","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":5991,"byte_end":6007,"line_start":206,"line_end":206,"column_start":18,"column_end":34,"is_primary":true,"text":[{"text":"    pub async fn receive_messages(&self, process_id: Uuid) -> Result<Vec<IpcMessage>> {","highlight_start":18,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":6515,"byte_end":6535,"line_start":222,"line_end":222,"column_start":18,"column_end":38,"is_primary":true,"text":[{"text":"    pub async fn create_shared_memory(","highlight_start":18,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":7312,"byte_end":7332,"line_start":245,"line_end":245,"column_start":18,"column_end":38,"is_primary":true,"text":[{"text":"    pub async fn attach_shared_memory(&mut self, name: &str, process_id: Uuid) -> Result<Uuid> {","highlight_start":18,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":7805,"byte_end":7825,"line_start":257,"line_end":257,"column_start":18,"column_end":38,"is_primary":true,"text":[{"text":"    pub async fn detach_shared_memory(&mut self, segment_id: Uuid, process_id: Uuid) -> Result<()> {","highlight_start":18,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":8466,"byte_end":8484,"line_start":273,"line_end":273,"column_start":18,"column_end":36,"is_primary":true,"text":[{"text":"    pub async fn read_shared_memory(","highlight_start":18,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":9095,"byte_end":9114,"line_start":293,"line_end":293,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"    pub async fn write_shared_memory(","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ipc.rs","byte_start":11724,"byte_end":11733,"line_start":372,"line_end":372,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn get_stats(&self) -> HashMap<String, usize> {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ipc.rs:166:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl IpcManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn create_channel(&mut self, process_id: Uuid) -> Result<Uuid> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn remove_channel(&mut self, process_id: Uuid) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_message(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn receive_messages(&self, process_id: Uuid) -> Result<Vec<IpcMessage>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn create_shared_memory(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m245\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn attach_shared_memory(&mut self, name: &str, process_id: Uuid) -> Result<Uuid> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn detach_shared_memory(&mut self, segment_id: Uuid, process_id: Uuid) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn read_shared_memory(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn write_shared_memory(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_stats(&self) -> HashMap<String, usize> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated functions `success` and `error` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":1398,"byte_end":1416,"line_start":72,"line_end":72,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl SyscallResult {","highlight_start":1,"highlight_end":19}],"label":"associated functions in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":1465,"byte_end":1472,"line_start":74,"line_end":74,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn success(return_value: i64, data: Option<Vec<u8>>) -> Self {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":1712,"byte_end":1717,"line_start":84,"line_end":84,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn error(error_message: String) -> Self {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated functions `success` and `error` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:74:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SyscallResult {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated functions in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Create a successful result\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn success(return_value: i64, data: Option<Vec<u8>>) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m84\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn error(error_message: String) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `memory_manager`, `scheduler`, `ipc_manager`, and `running` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":1959,"byte_end":1973,"line_start":95,"line_end":95,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct SyscallHandler {","highlight_start":12,"highlight_end":26}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":1980,"byte_end":1994,"line_start":96,"line_end":96,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    memory_manager: Arc<RwLock<MemoryManager>>,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":2028,"byte_end":2037,"line_start":97,"line_end":97,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    scheduler: Arc<RwLock<Scheduler>>,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":2067,"byte_end":2078,"line_start":98,"line_end":98,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    ipc_manager: Arc<RwLock<IpcManager>>,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":2109,"byte_end":2116,"line_start":99,"line_end":99,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    running: bool,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `memory_manager`, `scheduler`, `ipc_manager`, and `running` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:96:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m95\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct SyscallHandler {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    memory_manager: Arc<RwLock<MemoryManager>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    scheduler: Arc<RwLock<Scheduler>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ipc_manager: Arc<RwLock<IpcManager>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m99\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    running: bool,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/syscalls.rs","byte_start":2127,"byte_end":2146,"line_start":102,"line_end":102,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl SyscallHandler {","highlight_start":1,"highlight_end":20}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":2839,"byte_end":2853,"line_start":130,"line_end":130,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn handle_syscall(","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":5524,"byte_end":5532,"line_start":180,"line_end":180,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_fork(&self, parent_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":6069,"byte_end":6077,"line_start":191,"line_end":191,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_exec(&self, process_id: Uuid, _args: &[u64], _data: Option<Vec<u8>>) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":6330,"byte_end":6338,"line_start":197,"line_end":197,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_exit(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":6874,"byte_end":6882,"line_start":210,"line_end":210,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_wait(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":7053,"byte_end":7063,"line_start":215,"line_end":215,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"    async fn sys_getpid(&self, process_id: Uuid) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":7212,"byte_end":7220,"line_start":219,"line_end":219,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_kill(&self, _sender_id: Uuid, args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":7781,"byte_end":7789,"line_start":235,"line_end":235,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_mmap(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":8483,"byte_end":8493,"line_start":252,"line_end":252,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"    async fn sys_munmap(&self, _process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":9337,"byte_end":9344,"line_start":272,"line_end":272,"column_start":14,"column_end":21,"is_primary":true,"text":[{"text":"    async fn sys_brk(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":9837,"byte_end":9845,"line_start":284,"line_end":284,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_open(&self, _process_id: Uuid, _args: &[u64], _data: Option<Vec<u8>>) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":10030,"byte_end":10039,"line_start":288,"line_end":288,"column_start":14,"column_end":23,"is_primary":true,"text":[{"text":"    async fn sys_close(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":10172,"byte_end":10180,"line_start":292,"line_end":292,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_read(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":10345,"byte_end":10354,"line_start":296,"line_end":296,"column_start":14,"column_end":23,"is_primary":true,"text":[{"text":"    async fn sys_write(&self, _process_id: Uuid, _args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":10730,"byte_end":10738,"line_start":305,"line_end":305,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_seek(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":10892,"byte_end":10904,"line_start":311,"line_end":311,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"    async fn sys_msg_send(&self, sender_id: Uuid, args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":11659,"byte_end":11671,"line_start":327,"line_end":327,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"    async fn sys_msg_recv(&self, process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":12191,"byte_end":12205,"line_start":338,"line_end":338,"column_start":14,"column_end":28,"is_primary":true,"text":[{"text":"    async fn sys_shm_create(&self, process_id: Uuid, args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":13002,"byte_end":13016,"line_start":354,"line_end":354,"column_start":14,"column_end":28,"is_primary":true,"text":[{"text":"    async fn sys_shm_attach(&self, process_id: Uuid, _args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":13673,"byte_end":13687,"line_start":368,"line_end":368,"column_start":14,"column_end":28,"is_primary":true,"text":[{"text":"    async fn sys_shm_detach(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":14377,"byte_end":14389,"line_start":385,"line_end":385,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"    async fn sys_get_time(&self) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":14654,"byte_end":14667,"line_start":394,"line_end":394,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"    async fn sys_get_stats(&self) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":15434,"byte_end":15444,"line_start":415,"line_end":415,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"    async fn sys_socket(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":15607,"byte_end":15615,"line_start":419,"line_end":419,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"    async fn sys_bind(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":15748,"byte_end":15758,"line_start":423,"line_end":423,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"    async fn sys_listen(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":15891,"byte_end":15901,"line_start":427,"line_end":427,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"    async fn sys_accept(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/syscalls.rs","byte_start":16072,"byte_end":16083,"line_start":431,"line_end":431,"column_start":14,"column_end":25,"is_primary":true,"text":[{"text":"    async fn sys_connect(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {","highlight_start":14,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/syscalls.rs:130:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SyscallHandler {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn handle_syscall(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m180\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_fork(&self, parent_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_exec(&self, process_id: Uuid, _args: &[u64], _data: Option<Vec<u8>>) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_exit(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_wait(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_getpid(&self, process_id: Uuid) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m219\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_kill(&self, _sender_id: Uuid, args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m235\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_mmap(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_munmap(&self, _process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_brk(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m284\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_open(&self, _process_id: Uuid, _args: &[u64], _data: Option<Vec<u8>>) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_close(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_read(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_write(&self, _process_id: Uuid, _args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_seek(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_msg_send(&self, sender_id: Uuid, args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m327\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_msg_recv(&self, process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_shm_create(&self, process_id: Uuid, args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m354\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_shm_attach(&self, process_id: Uuid, _args: &[u64], data: Option<Vec<u8>>) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m368\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_shm_detach(&self, process_id: Uuid, args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m385\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_get_time(&self) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m394\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_get_stats(&self) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m415\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_socket(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m419\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_bind(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m423\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_listen(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m427\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_accept(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m431\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn sys_connect(&self, _process_id: Uuid, _args: &[u64]) -> Result<SyscallResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/process.rs","byte_start":1033,"byte_end":1045,"line_start":43,"line_end":43,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"impl Process {","highlight_start":1,"highlight_end":13}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":1088,"byte_end":1091,"line_start":45,"line_end":45,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(name: String, parent_id: Option<Uuid>) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":1701,"byte_end":1713,"line_start":64,"line_end":64,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn set_priority(&mut self, priority: ProcessPriority) {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":2198,"byte_end":2219,"line_start":82,"line_end":82,"column_start":12,"column_end":33,"is_primary":true,"text":[{"text":"    pub fn add_memory_allocation(&mut self, allocation_id: Uuid) {","highlight_start":12,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":2358,"byte_end":2382,"line_start":87,"line_end":87,"column_start":12,"column_end":36,"is_primary":true,"text":[{"text":"    pub fn remove_memory_allocation(&mut self, allocation_id: Uuid) {","highlight_start":12,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":2522,"byte_end":2531,"line_start":92,"line_end":92,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn open_file(&mut self, file_path: String) {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":2709,"byte_end":2719,"line_start":99,"line_end":99,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn close_file(&mut self, file_path: &str) {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":2853,"byte_end":2860,"line_start":104,"line_end":104,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn set_env(&mut self, key: String, value: String) {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":2998,"byte_end":3005,"line_start":109,"line_end":109,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn get_env(&self, key: &str) -> Option<&String> {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":3127,"byte_end":3148,"line_start":114,"line_end":114,"column_start":12,"column_end":33,"is_primary":true,"text":[{"text":"    pub fn set_working_directory(&mut self, path: String) {","highlight_start":12,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":3572,"byte_end":3578,"line_start":130,"line_end":130,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    pub fn uptime(&self) -> Duration {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:45:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Process {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// Create a new process\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m45\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(name: String, parent_id: Option<Uuid>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m64\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_priority(&mut self, priority: ProcessPriority) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_memory_allocation(&mut self, allocation_id: Uuid) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m87\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn remove_memory_allocation(&mut self, allocation_id: Uuid) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m92\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn open_file(&mut self, file_path: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m99\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn close_file(&mut self, file_path: &str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_env(&mut self, key: String, value: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_env(&self, key: &str) -> Option<&String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_working_directory(&mut self, path: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn uptime(&self) -> Duration {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `next_pid` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/process.rs","byte_start":3782,"byte_end":3796,"line_start":138,"line_end":138,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct ProcessManager {","highlight_start":12,"highlight_end":26}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":3842,"byte_end":3850,"line_start":140,"line_end":140,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    next_pid: u32,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `next_pid` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:140:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ProcessManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    processes: HashMap<Uuid, Process>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m140\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    next_pid: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/process.rs","byte_start":3860,"byte_end":3879,"line_start":143,"line_end":143,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl ProcessManager {","highlight_start":1,"highlight_end":20}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":4092,"byte_end":4106,"line_start":153,"line_end":153,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn create_process(&mut self, name: String, parent_id: Option<Uuid>) -> Result<Uuid> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":4962,"byte_end":4976,"line_start":177,"line_end":177,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn remove_process(&mut self, process_id: Uuid) -> Option<Process> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":5115,"byte_end":5129,"line_start":182,"line_end":182,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn list_processes(&self) -> Vec<&Process> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":5248,"byte_end":5271,"line_start":187,"line_end":187,"column_start":12,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn list_processes_by_state(&self, state: ProcessState) -> Vec<&Process> {","highlight_start":12,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":5488,"byte_end":5508,"line_start":195,"line_end":195,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"    pub fn list_child_processes(&self, parent_id: Uuid) -> Vec<&Process> {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":5734,"byte_end":5751,"line_start":203,"line_end":203,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"    pub fn kill_process_tree(&mut self, process_id: Uuid) -> Result<()> {","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":6361,"byte_end":6379,"line_start":224,"line_end":224,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn cleanup_terminated(&mut self) -> Vec<Process> {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":6877,"byte_end":6891,"line_start":242,"line_end":242,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn count_by_state(&self, state: ProcessState) -> usize {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/process.rs","byte_start":7050,"byte_end":7061,"line_start":247,"line_end":247,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn total_count(&self) -> usize {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/process.rs:153:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ProcessManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn create_process(&mut self, name: String, parent_id: Option<Uuid>) -> Result<Uuid> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn remove_process(&mut self, process_id: Uuid) -> Option<Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn list_processes(&self) -> Vec<&Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn list_processes_by_state(&self, state: ProcessState) -> Vec<&Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn list_child_processes(&self, parent_id: Uuid) -> Vec<&Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn kill_process_tree(&mut self, process_id: Uuid) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m224\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn cleanup_terminated(&mut self) -> Vec<Process> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn count_by_state(&self, state: ProcessState) -> usize {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m247\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn total_count(&self) -> usize {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"24 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 24 warnings emitted\u001b[0m\n\n"}
