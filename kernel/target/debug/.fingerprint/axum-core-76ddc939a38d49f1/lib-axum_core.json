{"rustc": 14389903092037495548, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 18014507388461277506, "profile": 5394829218943206528, "path": 14699275258938952772, "deps": [[504931904268503175, "http", false, 9404869949780815482], [902078471074753561, "async_trait", false, 6306417909651855740], [2543237566893615891, "bytes", false, 267094634672115108], [5204382251033773414, "tower_service", false, 15174287927695243003], [7470442545028885647, "mime", false, 17173162925417158882], [10821342338875855840, "tower_layer", false, 16699498820810628209], [11070927463981346568, "build_script_build", false, 184830355367536474], [13606258873719457095, "http_body", false, 17950474226657059866], [16476303074998891276, "futures_util", false, 2853325054736828015]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-76ddc939a38d49f1/dep-lib-axum_core"}}], "rustflags": [], "metadata": 14881847943984526847, "config": 2202906307356721367, "compile_kind": 0}