{"name": "distributed-kernel-bridge", "version": "1.0.0", "description": "WebSocket bridge service for Distributed Kernel", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node src/server.ts", "build": "tsc", "watch": "tsc -w"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "winston": "^3.11.0", "uuid": "^9.0.1", "@types/uuid": "^9.0.7"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/node": "^20.10.4", "typescript": "^5.3.3", "ts-node": "^10.9.1"}, "keywords": ["distributed", "kernel", "websocket", "bridge", "operating-system"], "author": "Distributed Kernel Team", "license": "MIT"}