import { EventEmitter } from 'events';
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'system-monitor' },
  transports: [
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ],
});

interface SystemStats {
  timestamp: string;
  uptime: number;
  memory: {
    total: number;
    used: number;
    free: number;
    usage_percent: number;
    cached: number;
    buffers: number;
    available: number;
    swap_total: number;
    swap_used: number;
    swap_free: number;
  };
  cpu: {
    usage_percent: number;
    cores: number;
    load_avg: number[];
    user_time: number;
    system_time: number;
    idle_time: number;
  };
  processes: {
    total: number;
    running: number;
    sleeping: number;
    stopped: number;
    zombie: number;
  };
  network: {
    interfaces: number;
    connections: number;
    bytes_rx: number;
    bytes_tx: number;
    packets_rx: number;
    packets_tx: number;
    errors_rx: number;
    errors_tx: number;
  };
  filesystem: {
    total_space: number;
    used_space: number;
    free_space: number;
    usage_percent: number;
    inodes_total: number;
    inodes_used: number;
    inodes_free: number;
  };
}

export class SystemMonitor extends EventEmitter {
  private running: boolean = false;
  private monitorInterval: NodeJS.Timeout | null = null;
  private statsHistory: SystemStats[] = [];
  private maxHistorySize: number = 100;
  private updateInterval: number = 2000; // 2 seconds

  // Simulation state
  private simulationState = {
    baseMemoryUsage: 0.4,
    baseCpuUsage: 0.2,
    networkBytesRx: 0,
    networkBytesTx: 0,
    networkPacketsRx: 0,
    networkPacketsTx: 0,
    processCount: 25,
    startTime: Date.now(),
  };

  constructor() {
    super();
  }

  public async start(): Promise<void> {
    if (this.running) {
      logger.warn('System monitor is already running');
      return;
    }

    this.running = true;
    logger.info('Starting system monitor');

    // Start monitoring loop
    this.monitorInterval = setInterval(() => {
      this.collectStats();
    }, this.updateInterval);

    // Collect initial stats
    this.collectStats();

    this.emit('started');
  }

  public async stop(): Promise<void> {
    if (!this.running) {
      return;
    }

    this.running = false;
    logger.info('Stopping system monitor');

    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    this.emit('stopped');
  }

  public isRunning(): boolean {
    return this.running;
  }

  public async getSystemStats(): Promise<SystemStats> {
    if (this.statsHistory.length === 0) {
      this.collectStats();
    }
    return this.statsHistory[this.statsHistory.length - 1];
  }

  public getStatsHistory(): SystemStats[] {
    return [...this.statsHistory];
  }

  private collectStats(): void {
    const now = new Date();
    const uptime = (Date.now() - this.simulationState.startTime) / 1000;

    // Simulate realistic system stats with some randomness
    const memoryUsage = this.simulationState.baseMemoryUsage + 
      (Math.sin(Date.now() / 30000) * 0.1) + 
      (Math.random() - 0.5) * 0.05;

    const cpuUsage = this.simulationState.baseCpuUsage + 
      (Math.sin(Date.now() / 20000) * 0.3) + 
      (Math.random() - 0.5) * 0.1;

    // Update network counters
    this.simulationState.networkBytesRx += Math.floor(Math.random() * 10000);
    this.simulationState.networkBytesTx += Math.floor(Math.random() * 8000);
    this.simulationState.networkPacketsRx += Math.floor(Math.random() * 100);
    this.simulationState.networkPacketsTx += Math.floor(Math.random() * 80);

    // Simulate process count changes
    this.simulationState.processCount += Math.floor((Math.random() - 0.5) * 3);
    this.simulationState.processCount = Math.max(15, Math.min(50, this.simulationState.processCount));

    const totalMemory = 1024 * 1024 * 1024; // 1GB
    const usedMemory = Math.floor(totalMemory * Math.max(0.1, Math.min(0.9, memoryUsage)));
    const freeMemory = totalMemory - usedMemory;

    const totalDisk = 10 * 1024 * 1024 * 1024; // 10GB
    const usedDisk = Math.floor(totalDisk * 0.3); // 30% used
    const freeDisk = totalDisk - usedDisk;

    const stats: SystemStats = {
      timestamp: now.toISOString(),
      uptime: Math.floor(uptime),
      memory: {
        total: totalMemory,
        used: usedMemory,
        free: freeMemory,
        usage_percent: (usedMemory / totalMemory) * 100,
        cached: Math.floor(totalMemory * 0.1),
        buffers: Math.floor(totalMemory * 0.05),
        available: freeMemory + Math.floor(totalMemory * 0.15),
        swap_total: 512 * 1024 * 1024, // 512MB
        swap_used: 0,
        swap_free: 512 * 1024 * 1024,
      },
      cpu: {
        usage_percent: Math.max(0, Math.min(100, cpuUsage * 100)),
        cores: 4,
        load_avg: [
          Math.max(0, Math.min(4, Math.random() * 2)),
          Math.max(0, Math.min(4, Math.random() * 1.8)),
          Math.max(0, Math.min(4, Math.random() * 1.5)),
        ],
        user_time: Math.floor(uptime * 0.6),
        system_time: Math.floor(uptime * 0.2),
        idle_time: Math.floor(uptime * 0.2),
      },
      processes: {
        total: this.simulationState.processCount,
        running: Math.floor(this.simulationState.processCount * 0.1),
        sleeping: Math.floor(this.simulationState.processCount * 0.8),
        stopped: Math.floor(this.simulationState.processCount * 0.05),
        zombie: Math.floor(this.simulationState.processCount * 0.05),
      },
      network: {
        interfaces: 2,
        connections: Math.floor(Math.random() * 20) + 5,
        bytes_rx: this.simulationState.networkBytesRx,
        bytes_tx: this.simulationState.networkBytesTx,
        packets_rx: this.simulationState.networkPacketsRx,
        packets_tx: this.simulationState.networkPacketsTx,
        errors_rx: Math.floor(Math.random() * 3),
        errors_tx: Math.floor(Math.random() * 2),
      },
      filesystem: {
        total_space: totalDisk,
        used_space: usedDisk,
        free_space: freeDisk,
        usage_percent: (usedDisk / totalDisk) * 100,
        inodes_total: 655360,
        inodes_used: Math.floor(655360 * 0.1),
        inodes_free: Math.floor(655360 * 0.9),
      },
    };

    // Add to history
    this.statsHistory.push(stats);

    // Trim history if too large
    if (this.statsHistory.length > this.maxHistorySize) {
      this.statsHistory.shift();
    }

    // Emit stats update event
    this.emit('stats', stats);

    logger.debug('Collected system stats', {
      cpu: stats.cpu.usage_percent.toFixed(1),
      memory: stats.memory.usage_percent.toFixed(1),
      processes: stats.processes.total,
    });
  }

  // Get specific metric history
  public getCpuHistory(points: number = 20): Array<{ timestamp: string; value: number }> {
    return this.statsHistory
      .slice(-points)
      .map(stat => ({
        timestamp: stat.timestamp,
        value: stat.cpu.usage_percent,
      }));
  }

  public getMemoryHistory(points: number = 20): Array<{ timestamp: string; value: number }> {
    return this.statsHistory
      .slice(-points)
      .map(stat => ({
        timestamp: stat.timestamp,
        value: stat.memory.usage_percent,
      }));
  }

  public getNetworkHistory(points: number = 20): Array<{ 
    timestamp: string; 
    bytes_rx: number; 
    bytes_tx: number; 
  }> {
    return this.statsHistory
      .slice(-points)
      .map(stat => ({
        timestamp: stat.timestamp,
        bytes_rx: stat.network.bytes_rx,
        bytes_tx: stat.network.bytes_tx,
      }));
  }

  // Get current system load
  public getCurrentLoad(): {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  } {
    const latest = this.statsHistory[this.statsHistory.length - 1];
    if (!latest) {
      return { cpu: 0, memory: 0, disk: 0, network: 0 };
    }

    return {
      cpu: latest.cpu.usage_percent,
      memory: latest.memory.usage_percent,
      disk: latest.filesystem.usage_percent,
      network: Math.min(100, (latest.network.bytes_rx + latest.network.bytes_tx) / 1000000 * 10),
    };
  }

  // Get system alerts
  public getAlerts(): Array<{
    type: 'warning' | 'error' | 'critical';
    message: string;
    timestamp: string;
  }> {
    const alerts = [];
    const latest = this.statsHistory[this.statsHistory.length - 1];
    
    if (!latest) {
      return alerts;
    }

    // CPU alerts
    if (latest.cpu.usage_percent > 90) {
      alerts.push({
        type: 'critical' as const,
        message: `High CPU usage: ${latest.cpu.usage_percent.toFixed(1)}%`,
        timestamp: latest.timestamp,
      });
    } else if (latest.cpu.usage_percent > 75) {
      alerts.push({
        type: 'warning' as const,
        message: `Elevated CPU usage: ${latest.cpu.usage_percent.toFixed(1)}%`,
        timestamp: latest.timestamp,
      });
    }

    // Memory alerts
    if (latest.memory.usage_percent > 90) {
      alerts.push({
        type: 'critical' as const,
        message: `High memory usage: ${latest.memory.usage_percent.toFixed(1)}%`,
        timestamp: latest.timestamp,
      });
    } else if (latest.memory.usage_percent > 80) {
      alerts.push({
        type: 'warning' as const,
        message: `Elevated memory usage: ${latest.memory.usage_percent.toFixed(1)}%`,
        timestamp: latest.timestamp,
      });
    }

    // Disk alerts
    if (latest.filesystem.usage_percent > 95) {
      alerts.push({
        type: 'critical' as const,
        message: `Disk space critical: ${latest.filesystem.usage_percent.toFixed(1)}%`,
        timestamp: latest.timestamp,
      });
    } else if (latest.filesystem.usage_percent > 85) {
      alerts.push({
        type: 'warning' as const,
        message: `Low disk space: ${latest.filesystem.usage_percent.toFixed(1)}%`,
        timestamp: latest.timestamp,
      });
    }

    return alerts;
  }
}
