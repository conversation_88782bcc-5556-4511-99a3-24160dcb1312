import { KernelConnector } from './kernel-connector';
import { ClientSession } from './server';
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'command-processor' },
  transports: [
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ],
});

interface CommandResult {
  success: boolean;
  output?: string;
  error?: string;
}

export class CommandProcessor {
  private kernelConnector: KernelConnector;
  private commandHistory: Map<string, string[]> = new Map();

  constructor(kernelConnector: KernelConnector) {
    this.kernelConnector = kernelConnector;
  }

  public async processCommand(command: string, session: ClientSession): Promise<CommandResult> {
    const trimmedCommand = command.trim();
    if (!trimmedCommand) {
      return { success: true, output: '' };
    }

    // Add to command history
    const history = this.commandHistory.get(session.id) || [];
    history.push(trimmedCommand);
    if (history.length > 100) {
      history.shift(); // Keep only last 100 commands
    }
    this.commandHistory.set(session.id, history);

    const [cmd, ...args] = trimmedCommand.split(/\s+/);
    
    try {
      switch (cmd.toLowerCase()) {
        case 'ps':
          return await this.handlePs(args);
        
        case 'ls':
          return await this.handleLs(args, session);
        
        case 'cat':
          return await this.handleCat(args);
        
        case 'echo':
          return await this.handleEcho(args);
        
        case 'pwd':
          return await this.handlePwd(session);
        
        case 'cd':
          return await this.handleCd(args, session);
        
        case 'mkdir':
          return await this.handleMkdir(args, session);
        
        case 'touch':
          return await this.handleTouch(args, session);
        
        case 'rm':
          return await this.handleRm(args, session);
        
        case 'top':
          return await this.handleTop();
        
        case 'netstat':
          return await this.handleNetstat();
        
        case 'history':
          return await this.handleHistory(session);
        
        case 'env':
          return await this.handleEnv(session);
        
        case 'export':
          return await this.handleExport(args, session);
        
        case 'kill':
          return await this.handleKill(args);
        
        case 'jobs':
          return await this.handleJobs();
        
        case 'df':
          return await this.handleDf();
        
        case 'free':
          return await this.handleFree();
        
        case 'uptime':
          return await this.handleUptime();
        
        default:
          return {
            success: false,
            error: `Command not found: ${cmd}. Type 'help' for available commands.`
          };
      }
    } catch (error) {
      logger.error(`Command execution error: ${error.message}`);
      return {
        success: false,
        error: `Command execution failed: ${error.message}`
      };
    }
  }

  private async handlePs(args: string[]): Promise<CommandResult> {
    const processes = await this.kernelConnector.getProcesses();
    
    let output = 'PID\tNAME\t\tSTATE\t\tCPU%\tMEM(MB)\tCREATED\n';
    output += '---\t----\t\t-----\t\t----\t------\t-------\n';
    
    for (const process of processes) {
      const created = new Date(process.created_at).toLocaleTimeString();
      output += `${process.id}\t${process.name.padEnd(12)}\t${process.state.padEnd(8)}\t${process.cpu_percent.toFixed(1)}\t${process.memory_mb}\t${created}\n`;
    }
    
    return { success: true, output };
  }

  private async handleLs(args: string[], session: ClientSession): Promise<CommandResult> {
    const path = args[0] || session.currentDirectory;
    const files = await this.kernelConnector.listFiles(path);
    
    let output = '';
    for (const file of files) {
      const permissions = file.permissions;
      const size = file.is_dir ? 'DIR' : file.size.toString();
      const modified = new Date(file.modified_at).toLocaleDateString();
      
      output += `${permissions}\t${file.owner}\t${file.group}\t${size.padStart(8)}\t${modified}\t${file.name}\n`;
    }
    
    return { success: true, output: output || 'Directory is empty' };
  }

  private async handleCat(args: string[]): Promise<CommandResult> {
    if (args.length === 0) {
      return { success: false, error: 'Usage: cat <filename>' };
    }
    
    try {
      const content = await this.kernelConnector.readFile(args[0]);
      return { success: true, output: content };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async handleEcho(args: string[]): Promise<CommandResult> {
    const output = args.join(' ');
    return { success: true, output };
  }

  private async handlePwd(session: ClientSession): Promise<CommandResult> {
    return { success: true, output: session.currentDirectory };
  }

  private async handleCd(args: string[], session: ClientSession): Promise<CommandResult> {
    const newPath = args[0] || session.environment.HOME || '/home/<USER>';
    
    // Simple path resolution (in production, this would be more sophisticated)
    if (newPath === '..') {
      const parts = session.currentDirectory.split('/').filter(p => p);
      parts.pop();
      session.currentDirectory = '/' + parts.join('/');
      if (session.currentDirectory === '/') session.currentDirectory = '/';
    } else if (newPath.startsWith('/')) {
      session.currentDirectory = newPath;
    } else {
      session.currentDirectory = session.currentDirectory === '/' 
        ? `/${newPath}` 
        : `${session.currentDirectory}/${newPath}`;
    }
    
    return { success: true, output: '' };
  }

  private async handleMkdir(args: string[], session: ClientSession): Promise<CommandResult> {
    if (args.length === 0) {
      return { success: false, error: 'Usage: mkdir <directory>' };
    }
    
    // Simulate directory creation
    return { success: true, output: `Directory created: ${args[0]}` };
  }

  private async handleTouch(args: string[], session: ClientSession): Promise<CommandResult> {
    if (args.length === 0) {
      return { success: false, error: 'Usage: touch <filename>' };
    }
    
    try {
      await this.kernelConnector.writeFile(args[0], '');
      return { success: true, output: `File created: ${args[0]}` };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async handleRm(args: string[], session: ClientSession): Promise<CommandResult> {
    if (args.length === 0) {
      return { success: false, error: 'Usage: rm <filename>' };
    }
    
    // Simulate file removal
    return { success: true, output: `File removed: ${args[0]}` };
  }

  private async handleTop(): Promise<CommandResult> {
    const processes = await this.kernelConnector.getProcesses();
    
    let output = 'System Monitor - Top Processes\n';
    output += '================================\n\n';
    output += 'PID\tNAME\t\tCPU%\tMEM(MB)\tSTATE\n';
    output += '---\t----\t\t----\t------\t-----\n';
    
    // Sort by CPU usage
    processes.sort((a, b) => b.cpu_percent - a.cpu_percent);
    
    for (const process of processes.slice(0, 10)) {
      output += `${process.id}\t${process.name.padEnd(12)}\t${process.cpu_percent.toFixed(1)}\t${process.memory_mb}\t${process.state}\n`;
    }
    
    return { success: true, output };
  }

  private async handleNetstat(): Promise<CommandResult> {
    const interfaces = await this.kernelConnector.getNetworkInfo();
    
    let output = 'Network Interfaces\n';
    output += '==================\n\n';
    
    for (const iface of interfaces) {
      output += `Interface: ${iface.name}\n`;
      output += `  IP Address: ${iface.ip_address}\n`;
      output += `  Netmask: ${iface.netmask}\n`;
      output += `  Gateway: ${iface.gateway || 'N/A'}\n`;
      output += `  MTU: ${iface.mtu}\n`;
      output += `  RX: ${iface.bytes_rx} bytes, ${iface.packets_rx} packets\n`;
      output += `  TX: ${iface.bytes_tx} bytes, ${iface.packets_tx} packets\n\n`;
    }
    
    return { success: true, output };
  }

  private async handleHistory(session: ClientSession): Promise<CommandResult> {
    const history = this.commandHistory.get(session.id) || [];
    let output = '';
    
    history.forEach((cmd, index) => {
      output += `${(index + 1).toString().padStart(4)}: ${cmd}\n`;
    });
    
    return { success: true, output: output || 'No command history' };
  }

  private async handleEnv(session: ClientSession): Promise<CommandResult> {
    let output = '';
    for (const [key, value] of Object.entries(session.environment)) {
      output += `${key}=${value}\n`;
    }
    return { success: true, output };
  }

  private async handleExport(args: string[], session: ClientSession): Promise<CommandResult> {
    if (args.length === 0) {
      return this.handleEnv(session);
    }
    
    const assignment = args[0];
    const [key, value] = assignment.split('=', 2);
    
    if (!value) {
      return { success: false, error: 'Usage: export KEY=value' };
    }
    
    session.environment[key] = value;
    return { success: true, output: '' };
  }

  private async handleKill(args: string[]): Promise<CommandResult> {
    if (args.length === 0) {
      return { success: false, error: 'Usage: kill <pid>' };
    }
    
    try {
      await this.kernelConnector.killProcess(args[0]);
      return { success: true, output: `Process ${args[0]} terminated` };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  private async handleJobs(): Promise<CommandResult> {
    return { success: true, output: 'No background jobs' };
  }

  private async handleDf(): Promise<CommandResult> {
    const output = `Filesystem     1K-blocks    Used Available Use% Mounted on
/dev/vfs1        1048576  524288    524288  50% /
tmpfs             262144   16384    245760   7% /tmp
devfs                  0       0         0   0% /dev`;
    
    return { success: true, output };
  }

  private async handleFree(): Promise<CommandResult> {
    const output = `              total        used        free      shared  buff/cache   available
Mem:        1048576      524288      524288           0           0      524288
Swap:             0           0           0`;
    
    return { success: true, output };
  }

  private async handleUptime(): Promise<CommandResult> {
    const uptime = process.uptime();
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    
    const output = `up ${hours}:${minutes.toString().padStart(2, '0')}, 1 user, load average: 0.15, 0.12, 0.08`;
    
    return { success: true, output };
  }
}
