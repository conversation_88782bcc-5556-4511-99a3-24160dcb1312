import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import winston from 'winston';
import { v4 as uuidv4 } from 'uuid';

import { KernelConnector } from './kernel-connector';
import { CommandProcessor } from './command-processor';
import { SystemMonitor } from './system-monitor';

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'bridge' },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ],
});

interface ClientSession {
  id: string;
  socketId: string;
  userId: string;
  connectedAt: Date;
  lastActivity: Date;
  currentDirectory: string;
  environment: Record<string, string>;
}

class BridgeServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private kernelConnector: KernelConnector;
  private commandProcessor: CommandProcessor;
  private systemMonitor: SystemMonitor;
  private sessions: Map<string, ClientSession> = new Map();
  private port: number;

  constructor(port: number = 8080) {
    this.port = port;
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: ["http://localhost:3000", "http://localhost:3001"],
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.kernelConnector = new KernelConnector();
    this.commandProcessor = new CommandProcessor(this.kernelConnector);
    this.systemMonitor = new SystemMonitor();

    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
  }

  private setupMiddleware(): void {
    this.app.use(helmet({
      contentSecurityPolicy: false, // Disable for development
    }));
    this.app.use(compression());
    this.app.use(cors({
      origin: ["http://localhost:3000", "http://localhost:3001"],
      credentials: true
    }));
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        services: {
          kernel: this.kernelConnector.isConnected(),
          websocket: true,
          monitor: this.systemMonitor.isRunning()
        }
      });
    });

    // System stats endpoint
    this.app.get('/api/stats', async (req, res) => {
      try {
        const stats = await this.systemMonitor.getSystemStats();
        res.json(stats);
      } catch (error) {
        logger.error('Failed to get system stats', error);
        res.status(500).json({ error: 'Failed to get system stats' });
      }
    });

    // Process list endpoint
    this.app.get('/api/processes', async (req, res) => {
      try {
        const processes = await this.kernelConnector.getProcesses();
        res.json(processes);
      } catch (error) {
        logger.error('Failed to get processes', error);
        res.status(500).json({ error: 'Failed to get processes' });
      }
    });

    // File system endpoint
    this.app.get('/api/files', async (req, res) => {
      try {
        const path = req.query.path as string || '/';
        const files = await this.kernelConnector.listFiles(path);
        res.json(files);
      } catch (error) {
        logger.error('Failed to list files', error);
        res.status(500).json({ error: 'Failed to list files' });
      }
    });

    // Network info endpoint
    this.app.get('/api/network', async (req, res) => {
      try {
        const networkInfo = await this.kernelConnector.getNetworkInfo();
        res.json(networkInfo);
      } catch (error) {
        logger.error('Failed to get network info', error);
        res.status(500).json({ error: 'Failed to get network info' });
      }
    });

    // Catch-all for unknown routes
    this.app.use('*', (req, res) => {
      res.status(404).json({ error: 'Route not found' });
    });
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);

      // Create session
      const session: ClientSession = {
        id: uuidv4(),
        socketId: socket.id,
        userId: 'user', // In production, this would come from authentication
        connectedAt: new Date(),
        lastActivity: new Date(),
        currentDirectory: '/home/<USER>',
        environment: {
          USER: 'user',
          HOME: '/home/<USER>',
          PATH: '/bin:/usr/bin',
          SHELL: '/bin/bash',
          TERM: 'xterm-256color'
        }
      };

      this.sessions.set(socket.id, session);

      // Send welcome message
      socket.emit('output', '\x1b[32m✓ Connected to Distributed Kernel Bridge\x1b[0m\r\n');
      socket.emit('output', '\x1b[36mSession ID: ' + session.id + '\x1b[0m\r\n');

      // Handle commands
      socket.on('command', async (data: { command: string }) => {
        try {
          session.lastActivity = new Date();
          logger.info(`Command received from ${socket.id}: ${data.command}`);

          const result = await this.commandProcessor.processCommand(
            data.command,
            session
          );

          socket.emit('command_result', result);
        } catch (error) {
          logger.error('Command processing error', error);
          socket.emit('command_result', {
            success: false,
            error: 'Command processing failed',
            output: null
          });
        }
      });

      // Handle file operations
      socket.on('file_read', async (data: { path: string }) => {
        try {
          const content = await this.kernelConnector.readFile(data.path);
          socket.emit('file_content', { path: data.path, content });
        } catch (error) {
          socket.emit('file_error', { path: data.path, error: error.message });
        }
      });

      socket.on('file_write', async (data: { path: string; content: string }) => {
        try {
          await this.kernelConnector.writeFile(data.path, data.content);
          socket.emit('file_saved', { path: data.path });
        } catch (error) {
          socket.emit('file_error', { path: data.path, error: error.message });
        }
      });

      // Handle process operations
      socket.on('process_kill', async (data: { pid: string }) => {
        try {
          await this.kernelConnector.killProcess(data.pid);
          socket.emit('process_killed', { pid: data.pid });
        } catch (error) {
          socket.emit('process_error', { pid: data.pid, error: error.message });
        }
      });

      socket.on('process_create', async (data: { name: string; priority: string }) => {
        try {
          const process = await this.kernelConnector.createProcess(data.name, data.priority);
          socket.emit('process_created', process);
        } catch (error) {
          socket.emit('process_error', { error: error.message });
        }
      });

      // Handle system monitoring requests
      socket.on('subscribe_stats', () => {
        logger.info(`Client ${socket.id} subscribed to system stats`);
        // Start sending periodic updates
        const interval = setInterval(async () => {
          if (!socket.connected) {
            clearInterval(interval);
            return;
          }

          try {
            const stats = await this.systemMonitor.getSystemStats();
            socket.emit('stats_update', stats);
          } catch (error) {
            logger.error('Failed to send stats update', error);
          }
        }, 2000);

        socket.on('disconnect', () => {
          clearInterval(interval);
        });
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
        this.sessions.delete(socket.id);
      });

      // Handle errors
      socket.on('error', (error) => {
        logger.error(`Socket error for ${socket.id}:`, error);
      });
    });
  }

  public async start(): Promise<void> {
    try {
      // Initialize kernel connection
      await this.kernelConnector.connect();
      logger.info('Connected to kernel');

      // Start system monitor
      await this.systemMonitor.start();
      logger.info('System monitor started');

      // Start HTTP server
      this.server.listen(this.port, () => {
        logger.info(`Bridge server running on port ${this.port}`);
        logger.info(`WebSocket endpoint: ws://localhost:${this.port}`);
        logger.info(`HTTP API endpoint: http://localhost:${this.port}`);
      });

      // Graceful shutdown handling
      process.on('SIGTERM', () => this.shutdown());
      process.on('SIGINT', () => this.shutdown());

    } catch (error) {
      logger.error('Failed to start bridge server', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    logger.info('Shutting down bridge server...');

    try {
      // Close WebSocket connections
      this.io.close();

      // Disconnect from kernel
      await this.kernelConnector.disconnect();

      // Stop system monitor
      await this.systemMonitor.stop();

      // Close HTTP server
      this.server.close(() => {
        logger.info('Bridge server shut down successfully');
        process.exit(0);
      });

    } catch (error) {
      logger.error('Error during shutdown', error);
      process.exit(1);
    }
  }

  public getSessionCount(): number {
    return this.sessions.size;
  }

  public getSessions(): ClientSession[] {
    return Array.from(this.sessions.values());
  }
}

// Start the server
if (require.main === module) {
  const port = parseInt(process.env.PORT || '8080');
  const server = new BridgeServer(port);
  
  server.start().catch((error) => {
    logger.error('Failed to start server', error);
    process.exit(1);
  });
}

export { BridgeServer, ClientSession };
