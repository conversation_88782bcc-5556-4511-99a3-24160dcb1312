# Multi-stage Dockerfile for Distributed Kernel
FROM rust:1.75 as rust-builder

WORKDIR /app
COPY kernel/ ./kernel/
WORKDIR /app/kernel
RUN cargo build --release

FROM golang:1.21 as go-builder

WORKDIR /app
COPY services/ ./services/
WORKDIR /app/services
RUN go mod tidy && \
    go build -o filesystem-service ./filesystem/ && \
    go build -o network-service ./network/

FROM node:18 as node-builder

WORKDIR /app
COPY bridge/ ./bridge/
WORKDIR /app/bridge
RUN npm install && npm run build

WORKDIR /app
COPY web/ ./web/
WORKDIR /app/web
RUN npm install && npm run build

FROM ubuntu:22.04

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    netcat \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js for bridge service
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

WORKDIR /app

# Copy built binaries and applications
COPY --from=rust-builder /app/kernel/target/release/kernel ./bin/
COPY --from=go-builder /app/services/filesystem-service ./bin/
COPY --from=go-builder /app/services/network-service ./bin/
COPY --from=node-builder /app/bridge/dist ./bridge/
COPY --from=node-builder /app/bridge/package*.json ./bridge/
COPY --from=node-builder /app/web/build ./web/

# Install bridge dependencies
WORKDIR /app/bridge
RUN npm install --production

WORKDIR /app

# Copy supervisor configuration
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create logs directory
RUN mkdir -p /var/log/distributed-kernel

# Expose ports
EXPOSE 3000 8080 50051 50052

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
