# Distributed Kernel Project

A modern distributed operating system kernel inspired by Linux, built with Rust and Go, accessible through a web interface.

## Architecture

### Core Components

1. **Microkernel (Rust)**: Minimal kernel handling essential operations
   - Memory management
   - Process scheduling  
   - Inter-process communication (IPC)
   - System calls

2. **Distributed Services (Go)**: User-space services
   - Filesystem service
   - Network stack
   - Device drivers
   - Authentication

3. **Web Interface (React + WebAssembly)**: Browser-based OS experience
   - Terminal emulator
   - System monitor
   - File manager
   - Process manager

4. **Communication Layer**: 
   - gRPC for service-to-service communication
   - WebSocket for web interface connectivity

## Getting Started

### Prerequisites
- Rust (latest stable)
- Go 1.21+
- Node.js 18+
- Docker (for containerized services)

### Building the Kernel
```bash
cd kernel
cargo build --release
```

### Running Services
```bash
cd services
go run ./...
```

### Starting Web Interface
```bash
cd web
npm install
npm start
```

## Project Structure

```
├── kernel/          # Rust microkernel
├── services/        # Go microservices
├── web/            # React web interface
├── api/            # gRPC API definitions
├── bridge/         # WebSocket bridge service
└── config/         # System configuration
```

## Features

- [x] Microkernel architecture
- [x] Distributed service model
- [x] Web-based interface
- [x] Real-time system monitoring
- [x] Process management
- [x] Virtual filesystem
- [x] Network stack
- [x] Authentication system

## License

MIT License - see LICENSE file for details.
