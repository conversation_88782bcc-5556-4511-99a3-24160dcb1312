# Distributed Kernel Project

A modern distributed operating system kernel inspired by Linux, built with Rust and Go, accessible through a web interface.

## 🚀 Quick Start

### One-Command Startup
```bash
./start.sh
```

This will build and start all components automatically. The web interface will be available at `http://localhost:3000`.

### Docker Deployment
```bash
docker-compose up -d
```

## 🏗️ Architecture

### Core Components

1. **Microkernel (Rust)**: Minimal kernel handling essential operations
   - Memory management with garbage collection
   - Multi-level feedback queue scheduler
   - Inter-process communication (IPC)
   - System call interface

2. **Distributed Services (Go)**: User-space microservices
   - Virtual filesystem service
   - Network stack with virtual interfaces
   - Device driver framework
   - Authentication and security

3. **Web Interface (React + TypeScript)**: Full-featured browser-based OS
   - Terminal emulator with xterm.js
   - Real-time system monitoring
   - File manager with CRUD operations
   - Process manager with controls
   - Network monitoring dashboard

4. **Bridge Service (Node.js)**: WebSocket bridge
   - Real-time communication between web and kernel
   - Command processing and routing
   - System metrics aggregation
   - Session management

## 📁 Project Structure

```
distributed-kernel/
├── kernel/                 # Rust microkernel
│   ├── src/
│   │   ├── main.rs        # Kernel entry point
│   │   ├── memory.rs      # Memory management
│   │   ├── scheduler.rs   # Process scheduler
│   │   ├── ipc.rs         # Inter-process communication
│   │   ├── syscalls.rs    # System call handler
│   │   └── process.rs     # Process management
│   └── Cargo.toml
├── services/              # Go microservices
│   ├── common/           # Shared types and utilities
│   ├── filesystem/       # Virtual filesystem service
│   ├── network/          # Network stack service
│   └── go.mod
├── bridge/               # WebSocket bridge service
│   ├── src/
│   │   ├── server.ts     # Main bridge server
│   │   ├── kernel-connector.ts
│   │   ├── command-processor.ts
│   │   └── system-monitor.ts
│   └── package.json
├── web/                  # React web interface
│   ├── src/
│   │   ├── components/   # Reusable components
│   │   ├── pages/        # Main application pages
│   │   └── App.tsx
│   └── package.json
├── docker/               # Docker configuration
├── start.sh             # Startup script
├── Dockerfile           # Container definition
└── docker-compose.yml   # Multi-container setup
```

## ✨ Features

### Kernel Features
- ✅ Microkernel architecture with minimal trusted computing base
- ✅ Memory management with allocation tracking and garbage collection
- ✅ Multi-level feedback queue scheduler with priority levels
- ✅ Inter-process communication with message passing and shared memory
- ✅ Comprehensive system call interface
- ✅ Process lifecycle management

### System Services
- ✅ Virtual filesystem with hierarchical structure
- ✅ Network stack with virtual interfaces and routing
- ✅ Real-time system monitoring and metrics collection
- ✅ Process management with state tracking
- ✅ Memory allocation and deallocation

### Web Interface
- ✅ Full-featured terminal emulator with command history
- ✅ Real-time system dashboard with live charts
- ✅ Process manager with start/stop/kill operations
- ✅ File manager with create/read/update/delete operations
- ✅ Network monitoring with interface statistics
- ✅ Responsive design with dark theme

### Development Features
- ✅ Comprehensive logging and error handling
- ✅ Docker containerization support
- ✅ Health checks and monitoring
- ✅ Graceful shutdown handling
- ✅ Development and production modes

## 🛠️ Prerequisites

- **Rust** (latest stable) - [Install Rust](https://rustup.rs/)
- **Go** 1.21+ - [Install Go](https://golang.org/dl/)
- **Node.js** 18+ - [Install Node.js](https://nodejs.org/)
- **Docker** (optional) - [Install Docker](https://docs.docker.com/get-docker/)

## 📖 Usage

### Terminal Commands
The web terminal supports standard Unix-like commands:

```bash
# Process management
ps                    # List processes
top                   # System monitor
kill <pid>            # Terminate process

# File operations
ls [path]             # List directory
cat <file>            # Display file content
mkdir <dir>           # Create directory
touch <file>          # Create file
rm <file>             # Remove file

# System information
uptime                # System uptime
free                  # Memory usage
df                    # Disk usage
netstat               # Network connections

# Environment
pwd                   # Current directory
cd <path>             # Change directory
env                   # Environment variables
export KEY=value      # Set environment variable
```

### API Endpoints
The bridge service exposes REST APIs:

```
GET  /health          # Health check
GET  /api/stats       # System statistics
GET  /api/processes   # Process list
GET  /api/files       # File system
GET  /api/network     # Network information
```

### WebSocket Events
Real-time communication via WebSocket:

```javascript
// Connect to kernel
socket.emit('command', { command: 'ps' });

// Subscribe to system stats
socket.emit('subscribe_stats');

// File operations
socket.emit('file_read', { path: '/etc/hostname' });
socket.emit('file_write', { path: '/tmp/test.txt', content: 'Hello' });
```

## 🔧 Development

### Building Components

```bash
# Build kernel
cd kernel && cargo build --release

# Build services
cd services && go mod tidy

# Build bridge
cd bridge && npm install && npm run build

# Build web interface
cd web && npm install && npm run build
```

### Running in Development Mode

```bash
# Terminal 1: Start kernel
cd kernel && cargo run

# Terminal 2: Start filesystem service
cd services/filesystem && go run main.go

# Terminal 3: Start network service
cd services/network && go run main.go

# Terminal 4: Start bridge
cd bridge && npm run dev

# Terminal 5: Start web interface
cd web && npm start
```

## 🐳 Docker Deployment

### Basic Deployment
```bash
docker-compose up -d
```

### With Monitoring
```bash
docker-compose --profile monitoring up -d
```

This includes Prometheus and Grafana for advanced monitoring.

## 🧪 Testing

### Unit Tests
```bash
# Test Rust kernel
cd kernel && cargo test

# Test Go services
cd services && go test ./...

# Test TypeScript bridge
cd bridge && npm test

# Test React components
cd web && npm test
```

### Integration Tests
```bash
# Start system and run integration tests
./start.sh &
sleep 30
curl http://localhost:8080/health
curl http://localhost:3000
```

## 📊 Monitoring

The system includes comprehensive monitoring:

- **System Metrics**: CPU, memory, disk, network usage
- **Process Monitoring**: Process states, resource usage
- **Network Statistics**: Interface statistics, connections
- **Application Logs**: Structured logging for all components
- **Health Checks**: Service availability monitoring

Access monitoring at:
- Web Dashboard: `http://localhost:3000`
- Bridge API: `http://localhost:8080`
- Prometheus: `http://localhost:9090` (with monitoring profile)
- Grafana: `http://localhost:3001` (with monitoring profile)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by Linux kernel architecture
- Built with modern systems programming languages
- Uses industry-standard web technologies
- Follows microkernel design principles
